import fs, { existsSync, mkdirSync } from 'fs';
import {
  generateSpeechWithTimingFromText,
  getWordsAlignmentFromCharAlignment,
  removeLongFormWordsFromTranscript,
} from '../../../modules/elevenlabs';
import {
  getResponseFromAzureOpenAi,
  getResponseFromOpenAi,
} from '../../../modules/openai';
import { type IAuthUser } from '../../../types';
import {
  BANNERBOT_FIRESTORE_COLLECTIONS,
  BANNERBOT_S3_PUBLIC_BUCKET_NAME,
  S3_BANNERBOT_VIDEO_MAKER_AUDIO_UPLOAD_DIR,
  TMP_BANNERBOT_AUDIO_UPLOAD_DIR,
} from '../../../constants';
import { v4 as uuidv4 } from 'uuid';
import { uploadFileToS3 } from '../../../modules/aws/s3/index';
import { getVideosFromPexels } from '../../../modules/pexels';
import logger from '../../../modules/logger';
import { bannerbotDb } from '../../../modules/firebase_admin_sdk';
import { Timestamp } from 'firebase-admin/firestore';
import ValidationError from '../../../utils/validation_error';
import { getSingleProject } from '../../bannerbot/bannerbot_controller';
import {
  type IBannerbotBusinessDetails,
  type IBannerbotProject,
} from '../../../types/bannerbot';
import { getSampleVideoData } from '../../../modules/fp-video-service';
import {
  type IFpVideoData,
  type FpVideoTemplates,
  type IFpVideoDataV2,
} from '../../../modules/fp-video-service/types';
import {
  getBaseVideoFromProductPrompt,
  getImageAndVideoKeywordsPromptV2,
  getVideoAdDirectorPromptV4,
  getVideoDataPrompt,
  getVideoRelevancePrompt,
  getVoiceOverScriptV3Prompt,
} from '../../../prompts/bannerbot_prompts';
import { AdLanguage } from '../../../types/campaign_details';
import { type IPexelsVideoData } from '../../../types/stock_image';
import { getHdVideoUrlsFromPexelsData } from '../../../utils/index';
import { addBgMusicToAudio } from '../../../modules/ffmpeg';
import {
  generateAiVideo,
  getTaskStatus,
  POLLO_AI_VIDEO_ASPECT_RATIO,
  POLLO_AI_VIDEO_LENGTH,
  POLLO_AI_VIDEO_RESOLUTION,
  POLLO_AI_VIDEO_ROUTES,
} from '../../../modules/pollo_ai';
import { getVideosFromStoryblocks } from '../../../modules/storyblocks';
import videoLogger from '../../../modules/video_logger';
import { getImagesFromFreepik } from '../../../modules/freepik';

// TODO: clean
// const sanitizeDevanagariText = (text: string): string => {
//   return (
//     text
//       // Remove Devanagari punctuation
//       .replace(/।/g, '') // Devanagari danda (।)
//       .replace(/॥/g, '') // Devanagari double danda (॥)

//       // Remove English punctuation
//       .replace(/[.,!?;:"'()-]/g, '') // Common punctuation
//       .replace(/\./g, '') // Periods (escaped properly)

//       // Remove extra whitespace
//       .replace(/\s+/g, ' ') // Multiple spaces to single space
//       .trim()
//   ); // Remove leading/trailing spaces
// };

// to fix the errors occured during production
const addVideoDataErrorToFirebase = async (params: object): Promise<void> => {
  const newVideoErrorsDoc = bannerbotDb
    .collection(BANNERBOT_FIRESTORE_COLLECTIONS.NEW_VIDEO_ERRORS)
    .doc();
  await newVideoErrorsDoc.set({ ...params, created_at: Timestamp.now() }, {});
};

export const getHdVideoUrlWithDurationFromPexels = async (
  keyword: string,
  useStoryblocks = false, // testing for now
): Promise<{
  hdVideoUrls: Array<{ url: string; duration: number }>;
  relevantVideos: IPexelsVideoData[];
}> => {
  logger.info(
    `searching for keyword ${keyword} in ${
      useStoryblocks ? 'storyblocks' : 'pexels'
    }`,
  );

  if (useStoryblocks) {
    const relevantVideos = await getVideosFromStoryblocks([keyword]);
    const hdVideoUrls = relevantVideos.map((item) => ({
      url: item.preview_urls._720p,
      duration: item.duration,
    }));
    return { hdVideoUrls, relevantVideos: [] };
  }

  const relevantVideos = await getVideosFromPexels({
    queries: [keyword],
    orientation: 'portrait',
  });

  const hdVideoUrls = getHdVideoUrlsFromPexelsData(relevantVideos);

  return { hdVideoUrls, relevantVideos: [] };
};

const getBestHdVideoUrlWithDurationFromPexels = async (params: {
  keyword: string;
  businessDetails: Partial<IBannerbotBusinessDetails>;
}): Promise<{
  media: { url: string; mediaType: 'video' | 'image' };
}> => {
  const RELEVANCE_THRESHOLD = 80;

  const { keyword, businessDetails } = params;

  let videoId: string = '';
  let videoScore: number = 0;
  let relevantVideos: IPexelsVideoData[] | null = null;

  // find the video with the highest score
  // retry 3 times
  for (let i = 1; i <= 3; i++) {
    videoLogger.info(
      `searching for keyword ${keyword} in pexels for page ${i}`,
    );

    const tempRelevantVideos = await getVideosFromPexels({
      queries: [keyword],
      orientation: 'portrait',
      page: i,
    });

    videoLogger.info(`Fetched ${tempRelevantVideos.length} videos from Pexels`);

    // eg: for the url "https://www.pexels.com/video/video-of-forest-1448735/" we will get the output -> video of forest
    const idWithDescriptionArr = tempRelevantVideos.map((item) => ({
      id: item.id.toString(),
      description:
        item.url?.split('/')?.[4]?.split('-')?.slice(0, -1)?.join(' ') ?? '',
    }));

    const videoRelevancePrompt = getVideoRelevancePrompt({
      business_details: businessDetails,
      keyword,
      videos: idWithDescriptionArr,
    });

    const videoRelevancePromptResponse = (await getResponseFromOpenAi(
      videoRelevancePrompt,
    )) as Record<string, number>;

    let currScore = 0;
    let currId = '';

    for (const [key, value] of Object.entries(videoRelevancePromptResponse)) {
      if (value > currScore) {
        currId = key;
        currScore = value;
      }
    }

    if (currId > videoId) {
      videoId = currId;
      videoScore = currScore;
      relevantVideos = tempRelevantVideos;
    }

    if (videoScore >= RELEVANCE_THRESHOLD) {
      break;
    }

    videoLogger.info(
      `Failed to find relevant video for keyword ${keyword} on page ${i}. Highest score: ${currScore}`,
    );
  }

  // fallback when the Relevant video is not found
  if (videoScore < RELEVANCE_THRESHOLD) {
    const freepikResponse = await getImagesFromFreepik(
      keyword,
      '15',
      'portrait',
    );

    if (freepikResponse.length) {
      videoLogger.info(
        `used the freepik image fallback for the keyword ${keyword}`,
      );

      // since the response is sorted on the basis of relevance taking the top one
      return {
        media: {
          url: freepikResponse[0].url,
          mediaType: 'image',
        },
      };
    }
  }

  const filteredPexelResponse =
    relevantVideos?.filter((item) => item.id === parseInt(videoId)) ?? [];

  if (!filteredPexelResponse.length)
    throw new Error(`missing video with id ${videoId} in response from pexels`);

  const [hdVideoUrl] = getHdVideoUrlsFromPexelsData(filteredPexelResponse);

  videoLogger.info(
    `Selected best video with score: ${videoScore} for the keyword ${keyword}. URL -> ${hdVideoUrl.url}`,
  );

  return { media: { ...hdVideoUrl, mediaType: 'video' } };
};

const getMultipleVideoScenesData = async ({
  businessDetails,
  script,
  transcript,
  ref,
  useStoryblocks = false,
}: {
  businessDetails: Partial<IBannerbotBusinessDetails>;
  script: string;
  transcript: Array<{
    text: string;
    start: number;
    end: number;
  }>;
  ref: {
    videoScenes: IFpVideoDataV2['scenes'] | undefined;
    videoAdDirectorResponse:
      | {
          chunks: Array<{
            script: string;
            keyword: string;
          }>;
        }
      | undefined;
  };
  useStoryblocks?: boolean; // testing for now
}): Promise<void> => {
  const videoAdDirectorPrompt = getVideoAdDirectorPromptV4({
    business_details: businessDetails,
    script,
  });
  ref.videoAdDirectorResponse = (await getResponseFromAzureOpenAi(
    videoAdDirectorPrompt,
  )) as { chunks: Array<{ script: string; keyword: string }> };

  videoLogger.info(
    `Director response: ${JSON.stringify(ref.videoAdDirectorResponse)}`,
  );

  let transcriptCursor = 0;
  let globalEndTime = 0;
  const chunkTimingData: Array<{
    chunk: {
      script: string;
      keyword: string;
    };
    startTime: number;
    endTime: number | undefined;
    sceneId: number;
  }> = [];

  logger.info('adding scenes videos start');

  for (const chunk of ref.videoAdDirectorResponse.chunks) {
    if (chunk.script === '') continue;

    const scriptArr = chunk.script.split(' ');

    transcriptCursor = transcriptCursor + scriptArr.length - 1;

    if (transcriptCursor >= transcript.length)
      throw new Error(
        'transcriptCursor exceeded transcript array length in getMultipleVideoScenesData',
      );

    const endIndex = transcriptCursor;

    const startTime = globalEndTime;
    const endTime = transcript[endIndex].end ?? 0;

    // only push if the chunk is of resoanable time
    if (endTime - startTime > 1.5) {
      globalEndTime = endTime;

      chunkTimingData.push({
        chunk,
        startTime,
        endTime,
        sceneId: chunkTimingData.length + 1,
      });
    }
  }

  // Step 2: Fetch all media in parallel
  const mediaPromises = chunkTimingData.map(
    async ({ chunk, startTime, endTime, sceneId }) => {
      const mediaData = await getBestHdVideoUrlWithDurationFromPexels({
        keyword: chunk.keyword,
        businessDetails,
      });

      if (
        startTime === undefined ||
        endTime === undefined ||
        !mediaData.media.url
      ) {
        throw new Error(
          'missing data in startTime or endTime or hdVideoUrls in createVideo',
        );
      }

      return {
        scene_id: sceneId,
        assets: [{ type: mediaData.media.mediaType, url: mediaData.media.url }],
        start: startTime,
        end: endTime,
      };
    },
  );

  ref.videoScenes = await Promise.all(mediaPromises);
  videoLogger.info(`✅ All scenes processed: `, ref.videoScenes);
};

export const getVideoDataUsingDetailsV2 = async ({
  businessDetails,
  language,
  templateId,
  generateAiBaseVideo = false,
  getMediaSuggestions = false,
  useStoryblocks = false,
}: {
  templateId: FpVideoTemplates;
  businessDetails: Partial<IBannerbotBusinessDetails>;
  language: AdLanguage;
  generateAiBaseVideo?: boolean;
  getMediaSuggestions?: boolean;
  useStoryblocks?: boolean;
}): Promise<{
  videoData: IFpVideoData | null;
  mediaSuggestion?: IPexelsVideoData[]; // mediaSuggestions will be given when branding details are in the sampleVideoData
}> => {
  /**
   * overall flow of generating video data for the new videos
   * - fist generate the script
   * - then generate the voice from the script
   * - then generate the subtitle using the voice
   * - then generate different scenes using the script
   * - add the timing to the scenes using the subtitle
   */

  // making a object since the value needs to be mutated inside the function
  const ref: {
    filteredVoiceOverScript: string | undefined;
    scriptFromTranscript: string | undefined;
    videoScenes: IFpVideoDataV2['scenes'] | undefined;
    videoAdDirectorResponse:
      | {
          chunks: Array<{
            script: string;
            keyword: string;
          }>;
        }
      | undefined;
    transcript: Array<{ text: string; start: number; end: number }> | undefined;
    audioS3Url: string | undefined;
  } = {
    audioS3Url: undefined,
    transcript: undefined,
    videoAdDirectorResponse: undefined,
    videoScenes: undefined,
    scriptFromTranscript: undefined,
    filteredVoiceOverScript: undefined,
  };

  try {
    let sampleVideoData = getSampleVideoData(templateId);

    if (!sampleVideoData) {
      throw new Error('sampleVideoData not found for template');
    }

    // this is to add the company details like brand name, website and logo
    if (sampleVideoData.branding) {
      const videoDataPrompt = getVideoDataPrompt({
        businessDetails,
        sampleVideoData,
        language,
      });
      const videoData = (await getResponseFromAzureOpenAi(
        videoDataPrompt,
      )) as IFpVideoData;

      // remove the logo node if the url is not present
      if (videoData?.branding?.logo?.url === '') {
        videoData.branding.logo = undefined;
      }
      sampleVideoData = videoData;
    }

    // first generating the voice over script for the product
    const voiceOverScriptPrompt = getVoiceOverScriptV3Prompt({
      business_details: businessDetails,
      language,
    });
    const voiceOverScriptResponse = (await getResponseFromOpenAi(
      voiceOverScriptPrompt,
    )) as { script: string; highlights: string[] };

    logger.info(voiceOverScriptResponse);

    ref.filteredVoiceOverScript = voiceOverScriptResponse.script.replace(
      /\n/g,
      '',
    );

    // now use the script to generate the voice for elevenlabs
    if (!existsSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR)) {
      mkdirSync(TMP_BANNERBOT_AUDIO_UPLOAD_DIR, { recursive: true });
    }
    const audioFileName = `${Date.now()}-${uuidv4()}.mp3`;
    const audioFilePath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/${audioFileName}`;

    // use ffmpeg to add subtle bg music
    const audioWithBgMusicFilePath = `${TMP_BANNERBOT_AUDIO_UPLOAD_DIR}/bg-${audioFileName}`;

    const voiceOverScript = ref.filteredVoiceOverScript.replace(
      /\{([^}]+)\}\[[^\]]+\]/g,
      '$1',
    );

    logger.info(voiceOverScript);

    const speechResponse = await generateSpeechWithTimingFromText({
      text: voiceOverScript,
      outputFilePath: audioFilePath,
    });

    // try adding bg music to audio
    let finalAudioPath = audioWithBgMusicFilePath;
    try {
      await addBgMusicToAudio({
        inputAudioFilePath: audioFilePath,
        outputAudioFilePath: audioWithBgMusicFilePath,
      });
    } catch (error) {
      // adding bg music failed, use original audio path
      logger.error(error);
      finalAudioPath = audioFilePath;
    }

    ref.audioS3Url = await uploadFileToS3(
      BANNERBOT_S3_PUBLIC_BUCKET_NAME,
      finalAudioPath,
      `${S3_BANNERBOT_VIDEO_MAKER_AUDIO_UPLOAD_DIR}/${audioFileName}`,
    );

    // clean audio paths from machine after uploading to s3
    try {
      await fs.promises.unlink(audioFilePath);
      // await fs.promises.unlink(audioWithBgMusicFilePath);
    } catch (error) {
      logger.error(error);
    }

    // so we have characters alignment data but we need the word alignment data
    ref.transcript = getWordsAlignmentFromCharAlignment(
      speechResponse.alignment,
    );

    const newTranscript = removeLongFormWordsFromTranscript(
      ref.transcript,
      ref.filteredVoiceOverScript,
    );

    ref.transcript = newTranscript;

    logger.info('Audio using elevenlabs generated ', ref.audioS3Url);

    ref.scriptFromTranscript = ref.transcript.map((i) => i.text).join(' ');

    // now break the script into different part and for each part have different images/videos for specific template
    if (['p10'].includes(sampleVideoData.template_id)) {
      await getMultipleVideoScenesData({
        businessDetails,
        transcript: ref.transcript,
        script: ref.scriptFromTranscript,
        ref,
        useStoryblocks,
      });

      sampleVideoData.scenes = ref.videoScenes;
    }

    let mediaSuggestion: IPexelsVideoData[] | undefined;

    // if the sampleData contains base_assests fill it with proper details
    if (getMediaSuggestions || sampleVideoData.base_assets) {
      const mediaKeywordsPrompt = getImageAndVideoKeywordsPromptV2({
        businessDetails,
      });
      const openAiResponseForMediaKeywords = (await getResponseFromAzureOpenAi(
        mediaKeywordsPrompt,
      )) as {
        image_keywords: string[];
        video_content_description_involving_people: string;
      };

      const hdVideoData = await getHdVideoUrlWithDurationFromPexels(
        openAiResponseForMediaKeywords.video_content_description_involving_people,
        useStoryblocks,
      );

      if (getMediaSuggestions) {
        mediaSuggestion = hdVideoData.relevantVideos;
      }

      if (sampleVideoData.base_assets) {
        // TODO: not tested need api key for testing
        if (generateAiBaseVideo) {
          logger.info('generating AI base video');
          const baseVideoFromProductPrompt = getBaseVideoFromProductPrompt({
            business_details: businessDetails,
            script: ref.scriptFromTranscript,
          });

          const baseVideoFromProductResponse = (await getResponseFromOpenAi(
            baseVideoFromProductPrompt,
          )) as { prompt: string };

          logger.info(
            'ai base video prompt -> ',
            baseVideoFromProductResponse.prompt,
          );

          const { taskId } = await generateAiVideo(
            POLLO_AI_VIDEO_ROUTES['SEEDANCE_1.0_LITE'],
            {
              prompt: baseVideoFromProductResponse.prompt,
              length: POLLO_AI_VIDEO_LENGTH.FIVE_SECONDS,
              aspectRatio: POLLO_AI_VIDEO_ASPECT_RATIO.RATIO_9_16,
              resolution: POLLO_AI_VIDEO_RESOLUTION['480p'],
            },
          );

          logger.info('pollo ai taskId -> ', taskId);

          let videoUrl: string;

          while (true) {
            await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds

            const taskStatus = await getTaskStatus(taskId);

            if (!taskStatus.generations) {
              throw new Error('Invalid response: no generations returned');
            }

            logger.info(
              'current task status -> ',
              taskStatus.generations[0].status,
            );

            if (taskStatus.generations[0].status === 'succeed') {
              videoUrl = taskStatus.generations[0].url ?? '';
              break;
            }

            if (taskStatus.generations[0].status === 'failed') {
              throw new Error('Error in generating AI base video');
            }
          }

          logger.info('pollo ai base video url -> ', videoUrl);

          sampleVideoData.base_assets.push({
            type: 'video',
            url: videoUrl,
            video_duration: 5,
          });
        } else {
          const firstHdVideo = hdVideoData.hdVideoUrls?.[0];

          sampleVideoData.base_assets.push({
            type: 'video',
            url: firstHdVideo?.url ?? '',
            video_duration: firstHdVideo?.duration,
          });
        }
      }
    }

    // populate the data into sample
    sampleVideoData.base_audio = {
      url: ref.audioS3Url,
      subtitle: ref.transcript as IFpVideoDataV2['base_audio']['subtitle'],
    };
    sampleVideoData.duration_in_sec =
      (ref.transcript[ref.transcript.length - 1].end ?? 20) + 1;

    if (
      voiceOverScriptResponse.highlights &&
      (sampleVideoData.scenes ?? []).length === 0
    ) {
      sampleVideoData.scenes ??= [];
      sampleVideoData.scenes.push({
        scene_id: 1,
        start: 0,
        end: sampleVideoData.duration_in_sec,
        texts: voiceOverScriptResponse.highlights.map((item) => ({
          value: item.replace(/\{([^}]+)\}\[[^\]]+\]/g, '$2'),
        })),
      });
    }

    return {
      videoData: sampleVideoData,
      mediaSuggestion,
    };
  } catch (error) {
    logger.error(error);
    if (error instanceof Error && !(error instanceof ValidationError)) {
      // TODO : To be removed after few days of testing
      void addVideoDataErrorToFirebase({
        success: false,
        error: error.message,
        ...ref,
      });
      throw new Error('Something went wrong'); // throwing generic message
    }
    throw error;
  }
};

export const getVideoData = async (
  params: {
    templateId: FpVideoTemplates;
    projectId: string;
  },
  user?: IAuthUser,
  useStoryblocks = false,
): Promise<IFpVideoData | null> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const { templateId, projectId } = params;

  const projectDetails = (
    await getSingleProject(user.uid, projectId)
  )?.data() as IBannerbotProject;

  const response = await getVideoDataUsingDetailsV2({
    businessDetails: projectDetails.details.business_details,
    templateId,
    language: AdLanguage.ENGLISH,
    useStoryblocks,
  });

  return response.videoData;
};

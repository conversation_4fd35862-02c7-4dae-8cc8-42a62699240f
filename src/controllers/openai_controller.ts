import * as cheerio from 'cheerio';
import axios, { type AxiosError } from 'axios';

import logger from '../modules/logger';
import {
  getResponseFromAzureOpenAi,
  getResponseFromOpenAi,
  generateAiBannerImage as generateAiBannerImageFromOpenAi,
} from '../modules/openai';
import { getImagesFromUnsplash } from '../modules/unsplash';
import {
  AdLanguage,
  type ICampaign,
  type IFlexibleTargetingItem,
  type IAdCopy,
  type IBusinessDetails,
  type IFlexibleSpecItem,
  type ILeadgenFormQuestion,
  type ILeadgenForm,
  FlexibleTargetingItemType,
  MetaSpecialAdCategory,
  type ITargeting,
  type IGoogleLocationDetails,
} from '../types/campaign_details';
import templatesCollection1 from '../constants/templates';
import templatesCollection2 from '../constants/templates_set2';
import { type IBannerTemplate } from '../types/banner_template';
import {
  type ICustomBannerImageData,
  type IImageData,
} from '../types/stock_image';
import {
  type IAuthUser,
  type IAdCreativeData,
  type IParamsForAdCreative,
  type IVideoProductionResponse,
  type IBusinessDetailsFromWebsite,
  AdPlatforms,
} from '../types';
import {
  createMulterFile,
  deepCopy,
  getImageTransformationParams,
  getUpdatedCloudflareImageUrl,
  getUpdatedUnsplashImageUrl,
  saveFileToDisk,
} from '../utils';
import ValidationError from '../utils/validation_error';
import {
  deduplicateTargetingItems,
  getSingleCampaign,
  isGrowEasyAdmin,
  updateFirestoreCampaign,
} from './util';
import {
  getAiBannerImageGenPrompt,
  getAdBannersPrompt,
  getAdCopiesPrompt,
  getFlexibleSpecTargetingPrompts,
  getLeadgenFormPrompt,
  getMetaDataExtractionPrompt,
  getPromptForFilteringImages,
  getSpecialAdCategoriesPrompt,
  getVideoMakerPromptV2,
  getVideoScriptsPrompt,
  getAdLanguageSuggestonsPrompt,
  getUspAndBannerElementDetailsPrompt,
  getIdealCustomersPrompt,
  getMetaAudienceKeywordsPrompt,
  getAdCopiesPromptV2,
  getUspAndBannerElementDetailsPromptV2,
  getAiBannerImageGenPromptV3,
} from '../prompts/groweasy_prompts';
import businessCategoriesWithAudience from '../constants/business_categories_with_audience';
import { getImagesFromPexels } from '../modules/pexels';
import {
  type IVideoDataV2,
  type IVideoTemplate,
} from '../types/video_template';
import {
  generateAudioUsingTemplate,
  generateVideoUsingTemplate,
} from '../modules/ffmpeg';
import videoTemplates from '../constants/video_templates';
// import tataModelImages from '../data/images/auto-dealers-new/tata';
import toyotaModelImages from '../data/images/auto-dealers-new/toyota';
import { getImagesFromFreepik } from '../modules/freepik';
import {
  GROWEASY_CLOUDFLARE_URL,
  GROWEASY_MAIN_AD_ACC_ID,
  TMP_ADIMAGES_UPLOAD_DIR,
} from '../constants';
import { generateAiBannerImage as generateAiBannerImageFromIdeogram } from '../modules/ideogram';
import { getAudienceSearch, uploadAdImage } from './meta_controller';
import { getCampaignDetails } from './db_controller';
import { generateImageWithImagen4 } from '../modules/gemini';
import { getCampaignDetailsForGrowEasyAdmin } from './admin_controller';

export const updateVariablesInTemplates = (params: {
  templates: IBannerTemplate[];
  banners: IAdCreativeData[];
  images: IImageData[] | ICustomBannerImageData[];
  businessLogo?: {
    url: string;
    width: number;
    height: number;
  };
  businessName?: string;
}): void => {
  const { templates, banners, images, businessLogo, businessName } = params;
  templates.forEach((template, index) => {
    const bannerData = banners[index % banners.length];
    const imageData = images[index % images.length];

    // iterate over template elements and substitute dynamic data
    template.elements.forEach((element) => {
      switch (element.type) {
        case 'textArr': {
          element.texts?.forEach((text) => {
            if (
              text.variableName === 'CREATIVE_TITLE_TEXT' &&
              bannerData.creative_title
            ) {
              text.value = bannerData.creative_title;
            } else if (
              text.variableName === 'CALL_OUT_TEXT' &&
              bannerData.call_out
            ) {
              text.value = bannerData.call_out;
            } else if (text.variableName === 'BUSINESS_NAME_TEXT') {
              // both logo & business name are required to render business name
              if (businessLogo?.url && businessName) {
                text.value = businessName;
              } else {
                text.value = ''; // in case of empty, will not be rendered
              }
            }
          });
          break;
        }
        case 'button': {
          if (element.textProps?.variableName === 'CTA_TEXT') {
            element.textProps.value = bannerData.call_to_action;
          }
          break;
        }
        case 'image': {
          if (element.imageProps?.variableName === 'CREATIVE_IMAGE') {
            let imagePropsUrl = '';
            const elementWidth = element.container?.width?.toString();
            const elementHeight = element.container?.height?.toString();

            // Freepik's default transformations add a watermark.
            // We use Cloudflare for transformations, but it increases image size when using template width & height.
            // To prevent this, we ensure the image size does not exceed its original dimensions.
            const transformationParams =
              'width' in imageData
                ? getImageTransformationParams({
                    imageWidth: imageData.width,
                    imageHeight: imageData.height,
                    targetAspectRatio:
                      parseInt(elementWidth) / parseInt(elementHeight),
                  })
                : null;

            if (
              'meta_data' in imageData &&
              imageData.meta_data?.cdn === 'cloudflare'
            ) {
              // Append Cloudflare transformation options directly in the path
              const cfTransformations = `width=${elementWidth},height=${elementHeight},quality=100,fit=cover`;
              imagePropsUrl = `${GROWEASY_CLOUDFLARE_URL}/${cfTransformations}/${imageData.meta_data.s3_url}`;
            } else if (
              imageData.url.startsWith(
                'https://bannerbot-public.s3.ap-south-1.amazonaws.com',
              ) ||
              imageData.url.startsWith(
                'https://groweasy-public.s3.ap-south-1.amazonaws.com',
              )
            ) {
              // s3 URL
              const cfTransformations = `width=${elementWidth},height=${elementHeight},quality=100,fit=cover`;
              imagePropsUrl = `${GROWEASY_CLOUDFLARE_URL}/${cfTransformations}/${imageData.url}`;
            } else if (imageData.url.includes('groweasy.ai/cdn-cgi/image')) {
              // url is already from Cloudflare CDN, just update it (Freepik)
              /* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
              imagePropsUrl = getUpdatedCloudflareImageUrl(imageData.url, {
                width: transformationParams?.targetWidth || elementWidth,
                height: transformationParams?.targetHeight || elementHeight,
              });
              /* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
            } else {
              // unsplash/pexels URLs
              imagePropsUrl = getUpdatedUnsplashImageUrl(imageData.url, {
                width: elementWidth,
                height: elementHeight,
              });
            }

            element.imageProps.url = imagePropsUrl;
          } else if (element.imageProps?.variableName === 'LOGO_IMAGE') {
            // independent of business name
            element.imageProps.url = businessLogo?.url ?? ''; // in case of empty, will not be rendered
          }
          break;
        }
        default:
      }
    });
  });
};

export const getAdBanners = async (
  params: IParamsForAdCreative & {
    google_geo_locations?: IGoogleLocationDetails[];
    ai_assisted_product_usps?: string[];
  },
): Promise<{
  templates: IBannerTemplate[];
  images: IImageData[] | ICustomBannerImageData[];
}> => {
  let banners: IAdCreativeData[] = [];
  const prompt = getAdBannersPrompt(params);
  // console.log(prompt)
  const openAiResponse = await getResponseFromAzureOpenAi(prompt);
  if (openAiResponse?.banners?.length) {
    banners = openAiResponse.banners;
  }
  // console.dir(openAiResponse, { depth: null });
  logger.info(openAiResponse);
  let images: IImageData[] | ICustomBannerImageData[] = [];

  // industry specific images handling
  if (params.business_details?.business_category === 'Auto Dealers - New') {
    images = toyotaModelImages.filter(
      (item) => item.meta_data?.model === 'hilux',
    );
  } else {
    logger.info(
      `Searching for ${banners[0].creative_image_keywords?.join(
        ',',
      )} and ${banners[1].creative_image_keywords?.join(',')}`,
    );
    const [freepikImages1, freepikImages2, freepikImages3] = await Promise.all([
      getImagesFromFreepik(banners[0].creative_image_keywords?.join(',')),
      getImagesFromFreepik(banners[1].creative_image_keywords?.join(',')),
      getImagesFromFreepik(banners[2].creative_image_keywords?.join(',')),
    ]);
    images = [...freepikImages1, ...freepikImages2, ...freepikImages3];

    // fallback just in case
    if (!images.length) {
      const [unsplashImages1, pexelsImages, unsplashImages2] =
        await Promise.all([
          getImagesFromUnsplash(banners[0].creative_image_keywords?.join(',')),
          getImagesFromPexels(banners[0].creative_image_keywords?.join(',')),
          getImagesFromUnsplash(banners[1].creative_image_keywords?.join(',')),
        ]);
      images = [...unsplashImages1, ...pexelsImages, ...unsplashImages2];
    }
  }
  const templates: IBannerTemplate[] = [];
  [...templatesCollection1, ...templatesCollection2]
    .reverse()
    // .filter((item) => ['square', 'portrait'].includes(item.size))
    .forEach((template) => {
      templates.push(deepCopy(template) as IBannerTemplate);
    });
  updateVariablesInTemplates({
    templates,
    banners,
    images,
    /* businessLogo: params?.business_details?.business_logo?.square, 
    businessName: params?.business_details?.business_name */
  });
  return {
    images,
    templates,
  };
};

export const getAdCopies = async (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
    version?: 'v1' | 'v2';
    platform?: AdPlatforms;
  },
): Promise<IAdCopy[]> => {
  let copies: IAdCopy[] = [];
  const version = params.version ?? 'v1';
  const prompt =
    version === 'v1' ? getAdCopiesPrompt(params) : getAdCopiesPromptV2(params);
  const openAiResponse = await getResponseFromOpenAi(prompt);
  if (openAiResponse?.copies?.length) {
    copies = openAiResponse.copies;
  }
  logger.debug(copies);
  return copies;
};

export const populateAdCopies = async (
  { campaignId, version }: { campaignId: string; version: 'v1' | 'v2' },
  user?: IAuthUser,
): Promise<IAdCopy[]> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const doc = await getSingleCampaign(user.uid, campaignId);
    if (doc?.id) {
      const campaign: ICampaign = doc.data();
      if (campaign?.details?.targeting && campaign?.details?.business_details) {
        let adCopies = await getAdCopies({
          targeting: campaign.details?.targeting,
          business_details: campaign.details?.business_details,
          // google ad copies are generated while launching the campaign, till then onboarding flow shows this data
          // for Google, Copies have to be always in English
          ad_language:
            campaign.platform === AdPlatforms.GOOGLE
              ? AdLanguage.ENGLISH
              : campaign.details?.ad_language,
          ai_assisted_product_usps: campaign.details?.ai_assisted_product_usps,
          version,
          platform: campaign.platform,
        });

        const count = 5; // select only 5 copies
        adCopies = adCopies.filter((item, index) => index < count);
        logger.debug(adCopies);

        // write to Firestore
        campaign.details.ad_copies = adCopies;
        await updateFirestoreCampaign(doc.id, {
          details: campaign.details,
        });
        return adCopies;
      } else {
        throw new ValidationError(400, 'This campaign has missing details');
      }
    } else {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

/* const getFilteredTargetingData = (
  flexibleSpecTargetingArr: IFlexibleTargetingItem[],
  type: FlexibleTargetingItemType,
): string => {
  const filteredArr = flexibleSpecTargetingArr
    .filter((item) => item.type === type)
    .map(({ id, name, key }) => ({ id, name, key }));

  return JSON.stringify(filteredArr);
};

export const getMetaAudience = async (payload: {
  targeting: ITargeting;
  businessDetails: IBusinessDetails;
}): Promise<Partial<IFlexibleSpecItem>> => {
  const { targeting, businessDetails } = payload;

  const targetingPrompts: string[] = [];
  const targetingItems: IFlexibleTargetingItem[] = [];
  const flexibleSpecItem: Partial<IFlexibleSpecItem> = {
    behaviors: [],
    interests: [],
    life_events: [],
    education_statuses: [],
    industries: [],
  };

  // Basic targeting from standard list
  [
    FlexibleTargetingItemType.behaviors,
    FlexibleTargetingItemType.interests,
    FlexibleTargetingItemType.work_positions,
    // FlexibleTargetingItemType.education_statuses,
    FlexibleTargetingItemType.industries,
  ].forEach((type) => {
    const prompt = getFlexibleSpecTargetingPrompts(
      { targeting, business_details: businessDetails },
      getFilteredTargetingData(
        Object.values(flexibleSpecTargetingDataObj),
        type,
      ),
    );
    targetingPrompts.push(prompt);
  });

  // Detailed targeting based on selected business category
  const categoryBasedTargetingSpec:
    | Record<string, IFlexibleTargetingItem>
    | undefined = businessCategoriesWithAudience.find(
    (item) =>
      item['business-category']?.trim() ===
      businessDetails.business_category?.trim(),
  )?.audiences;

  if (categoryBasedTargetingSpec) {
    const prompt = getFlexibleSpecTargetingPrompts(
      { targeting, business_details: businessDetails },
      getFilteredTargetingData(
        Object.values(categoryBasedTargetingSpec),
        FlexibleTargetingItemType.interests,
      ),
    );
    targetingPrompts.push(prompt);
  }

  // Generate targeting items from OpenAI
  const targetingResponses = await Promise.all(
    targetingPrompts.map(
      async (prompt) =>
        await getResponseFromOpenAi(prompt, {
          temperature: 0.3,
        }),
    ),
  );
  targetingResponses.forEach((response) => {
    targetingItems.push(...response.targeting);
  });

  // Map targeting items to flexibleSpecItem
  targetingItems.forEach((item) => {
    if (item.conversion_probability === 'high') {
      delete item.conversion_probability;
      const fullTargetingItem =
        flexibleSpecTargetingDataObj[item.id] ??
        categoryBasedTargetingSpec?.[item.id];

      if (fullTargetingItem?.type) {
        if (flexibleSpecItem[fullTargetingItem.type]) {
          flexibleSpecItem[fullTargetingItem.type]?.push(
            fullTargetingItem.type ===
              FlexibleTargetingItemType.education_statuses
              ? item.id
              : item,
          );
        } else {
          flexibleSpecItem[fullTargetingItem.type] = [
            fullTargetingItem.type ===
            FlexibleTargetingItemType.education_statuses
              ? item.id
              : item,
          ];
        }
      } else {
        logger.error(
          'Flexible spec targeting data mismatch or Missing type in Targeting data set',
        );
      }
    }
  });

  return flexibleSpecItem;
}; */

export const populateDetailedTargeting = async (
  campaignId: string,
  user?: IAuthUser,
): Promise<Partial<IFlexibleSpecItem>> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  try {
    let campaignDetailsDocSnapshot;
    if (isGrowEasyAdmin(user)) {
      campaignDetailsDocSnapshot =
        await getCampaignDetailsForGrowEasyAdmin(campaignId);
    } else {
      campaignDetailsDocSnapshot = await getSingleCampaign(
        user?.uid,
        campaignId,
      );
    }
    if (campaignDetailsDocSnapshot?.id) {
      const campaign: ICampaign = campaignDetailsDocSnapshot.data();
      const targetingDetails = campaign?.details?.targeting;
      const businessDetails = campaign?.details?.business_details;

      if (targetingDetails && businessDetails) {
        // Get Meta Audience
        const flexibleSpecItem = await getMetaAudienceV2({
          targeting: targetingDetails,
          businessDetails,
        });

        // Write to Firestore
        if (campaign?.details?.targeting) {
          campaign.details.targeting.flexible_spec = [flexibleSpecItem];
        }
        await updateFirestoreCampaign(campaignDetailsDocSnapshot.id, {
          details: campaign.details,
        });

        return flexibleSpecItem;
      } else {
        throw new ValidationError(400, 'This campaign has missing details');
      }
    } else {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getLeadgenQuestions = async (
  params: IParamsForAdCreative & {
    ai_assisted_product_usps?: string[];
  },
): Promise<ILeadgenFormQuestion[]> => {
  let questions: ILeadgenFormQuestion[] = [];
  const prompt = getLeadgenFormPrompt(params);
  const openAiResponse = await getResponseFromOpenAi(prompt);
  if (openAiResponse?.questions?.length) {
    questions = openAiResponse.questions;
  }
  logger.debug(questions);
  return questions;
};

export const generateLeadgenForm = async (
  campaignId: string,
  user?: IAuthUser,
): Promise<ILeadgenForm> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const doc = await getSingleCampaign(user.uid, campaignId);
    if (doc?.id) {
      const campaign: ICampaign = doc.data();
      if (campaign?.details?.targeting && campaign?.details?.business_details) {
        const questions = await getLeadgenQuestions({
          targeting: campaign.details?.targeting,
          business_details: campaign.details?.business_details,
          ad_language: campaign.details?.ad_language,
          ai_assisted_product_usps: campaign.details.ai_assisted_product_usps,
        });
        const leadgenForm: ILeadgenForm = {
          // timestamp will be appended while form creation at Meta
          // to avoid error: Form name already exists
          name: `${user?.email}_${campaign?.details?.business_details?.business_category}`,
          // caution, FULL_NAME -> question1 & PHONE -> question3 are hardcoded in CTWA
          questions: [
            { type: 'FULL_NAME', key: 'question1' },
            { type: 'EMAIL', key: 'question2' },
            { type: 'PHONE', key: 'question3' },
          ],
          is_optimized_for_quality: true,
          block_display_for_non_targeted_viewer: false,
          privacy_policy: {
            url: 'https://groweasy.ai/privacy-policy',
            link_text: 'Our Privacy Policy',
          },
          follow_up_action_url: campaign?.details?.business_details?.website
            ? campaign?.details?.business_details?.website
            : 'https://groweasy.ai/',
          follow_up_action_text: 'Visit our Website',
          context_card: {
            title: 'Please fill in below details',
            style: 'PARAGRAPH_STYLE',
            content: [
              'Please provide the details below. A member of our team will reach out to you.',
            ],
          },
          question_page_custom_headline:
            'We will use these details to get back to you.',
        };
        questions.forEach((question, index) => {
          // only 1 question, to increase volume
          if (index < 1) {
            leadgenForm.questions.push({
              ...question,
              // start with 4th question (question1, 2, 3 are reserved even if email is not being used)
              key: `question${3 + index + 1}`,
            });
          }
        });
        logger.debug(leadgenForm);

        // write to Firestore
        campaign.details.leadgen_form = leadgenForm;
        await updateFirestoreCampaign(doc.id, {
          details: campaign.details,
        });
        return leadgenForm;
      } else {
        throw new ValidationError(400, 'This campaign has missing details');
      }
    } else {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getSpecialAdCategories = async (params: {
  business_details: IBusinessDetails;
}): Promise<MetaSpecialAdCategory[]> => {
  let specialAdCategories: MetaSpecialAdCategory[] = [];
  const prompt = getSpecialAdCategoriesPrompt(params);
  const openAiResponse = await getResponseFromOpenAi(prompt);
  logger.debug(openAiResponse);
  if (
    openAiResponse?.category &&
    [
      // MetaSpecialAdCategory.CREDIT, // let it be rejected in Ads Manager
      // MetaSpecialAdCategory.EMPLOYMENT, // let it be rejected in Ads Manager
      // MetaSpecialAdCategory.HOUSING, // let it be rejected in Ads Manager
      MetaSpecialAdCategory.ISSUES_ELECTIONS_POLITICS,
    ].includes(openAiResponse.category)
  ) {
    specialAdCategories = [openAiResponse.category];
  }
  logger.debug(specialAdCategories);
  return specialAdCategories;
};

export const populateSpecialAdCategories = async (
  campaignId: string,
  user?: IAuthUser,
): Promise<MetaSpecialAdCategory[]> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const doc = await getSingleCampaign(user.uid, campaignId);
    if (doc?.id) {
      const campaign: ICampaign = doc.data();
      if (campaign?.details?.business_details) {
        const specialAdCategories = await getSpecialAdCategories({
          business_details: campaign.details?.business_details,
        });

        // write to Firestore
        await updateFirestoreCampaign(doc.id, {
          special_ad_categories: specialAdCategories,
        });
        return specialAdCategories;
      } else {
        throw new ValidationError(400, 'This campaign has missing details');
      }
    } else {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getVideoScripts = async (params: {
  prompt: string;
  language?: 'hi' | 'en';
}): Promise<IVideoProductionResponse> => {
  const prompt = getVideoScriptsPrompt(params);
  const openAiResponse = await getResponseFromAzureOpenAi(prompt);
  return openAiResponse;
};

/* const getHighlyRelevantVideosFromPexels = async (
  query: string,
): Promise<IPexelsVideoData[]> => {
  let videos = await getVideosFromPexels({
    queries: [query],
  });
  const filterVideosPrompt = getPromptForFilteringVideos(
    query,
    videos?.map((item) => ({
      id: item.id,
      url: item.url,
    })),
  );
  const filteredVideoData: {
    videos: Array<{ id: number | string }>;
  } = await getResponseFromAzureOpenAi(filterVideosPrompt);
  videos = videos.filter((daum: IPexelsVideoData) => {
    const match = filteredVideoData?.videos.some(
      (item) => item.id.toString() === daum.id.toString(),
    );
    return match;
  });
  return videos;
}; */

// query is comma separated string
const getHighlyRelevantImagesFromPexelsOrUnsplash = async (params: {
  query: string;
  provider?: 'pexels' | 'unsplash';
  productOrServiceDescription?: string;
}): Promise<IImageData[]> => {
  const { query, provider, productOrServiceDescription } = params;

  const imageProvider = provider ?? 'pexels';
  let images =
    imageProvider === 'pexels'
      ? await getImagesFromPexels(query)
      : await getImagesFromUnsplash(query);
  const filterImagesPrompt = getPromptForFilteringImages({
    imageKeywords: [query],
    imagesData: images,
    productOrServiceDescription,
  });
  const filteredImageData: {
    images: Array<Partial<IImageData>>;
  } = await getResponseFromAzureOpenAi(filterImagesPrompt);
  images = images.filter((daum: IImageData) => {
    const match = filteredImageData?.images.some((item) => item.id === daum.id);
    return match;
  });
  return images;
};

// multi frame video with human voiceover
export const getVideoDataV2 = async (
  params: IParamsForAdCreative & {
    google_tts: boolean;
  },
): Promise<{ template: IVideoTemplate; audio_url: string }> => {
  logger.info(
    'getVideoDataV2 called for propmt',
    params.business_details?.product_or_service_description,
  );
  const prompt = getVideoMakerPromptV2(params);
  const videoData: IVideoDataV2 = await getResponseFromAzureOpenAi(prompt);

  const pexelsImagesPromises: Array<Promise<IImageData[]>> = [];
  // const pexelsVideosPromises: Array<Promise<IPexelsVideoData[]>> = [];
  for (const frame of videoData.frames) {
    pexelsImagesPromises.push(
      getHighlyRelevantImagesFromPexelsOrUnsplash({
        query: frame.video_content_description_involving_people ?? '',
      }),
    );
    /* pexelsVideosPromises.push(
      getHighlyRelevantVideosFromPexels(
        frame.video_content_description_involving_people ?? '',
      ),
    ); */
  }
  const pexelsImagesResponses = await Promise.all(pexelsImagesPromises);
  // const pexelsVideosResponses = await Promise.all(pexelsVideosPromises);
  videoData.frames.forEach((frame, index) => {
    frame.images = pexelsImagesResponses[index];
    // frame.videos = pexelsVideosResponses[index];
  });

  // create a copy of template so that original one stays unmodified
  const videoTemplate = JSON.parse(
    JSON.stringify(videoTemplates[0]),
  ) as IVideoTemplate;

  // merge video data with video template
  videoTemplate.video_caption = videoData.video_caption;

  // frames length should always be equal
  // todo logo url after accepting in request body
  videoData.frames.forEach((item, index) => {
    const templateFrame = videoTemplate.frames[index];
    if (templateFrame) {
      templateFrame.voiceover_text = item.voiceover_text;
      if (templateFrame.textProps) {
        templateFrame.textProps.value = item.highlighted_text;
      }
      if (templateFrame.mediaProps) {
        if (templateFrame.mediaType === 'video') {
          templateFrame.mediaProps.url =
            item.videos?.[0]?.video_files?.[0]?.link ?? '';

          // in future
          // templateFrame.videos = item.videos;
        } else {
          templateFrame.mediaProps.url = item.images?.[0]?.url ?? '';
          templateFrame.images = item.images;
        }
      }
    } else {
      logger.error('generateVideo Frame mismatch', index);
    }
  });
  if (params?.business_details?.mobile?.includes('+91')) {
    // use Indian voice
    videoTemplate.voice = {
      languageCode: 'en-IN',
      name: 'en-IN-Wavenet-C',
      ssmlGender: 1,
    };
  }
  const audioUrl = await generateAudioUsingTemplate(videoTemplate);
  return {
    template: videoTemplate,
    audio_url: audioUrl,
  };
};

export const generateVideo = async (params: {
  template: IVideoTemplate;
}): Promise<{ url: string; caption: string }> => {
  const { template } = params;

  const data = await generateVideoUsingTemplate(template);

  return data;
};

const getWebsiteMetaData = async (params: {
  website: string;
}): Promise<{
  meta_tags: Record<string, string>;
  title: string;
  apple_touch_icon?: string;
}> => {
  let { website } = params;
  try {
    if (!/^https?:\/\//i.test(website)) {
      website = `http://${website}`;
    }
    // Fetch the website's HTML content
    const response = await axios.get(website);

    // Load the HTML into cheerio for parsing
    const $ = cheerio.load(response.data);

    // Extract meta tags
    const metaTags: Record<string, string> = {};
    $('meta').each((_, element) => {
      const name = $(element).attr('name') ?? $(element).attr('property');
      const content = $(element).attr('content');

      if (name && content) {
        metaTags[name] = content;
      }
    });
    if (!metaTags.url) {
      metaTags.url = website;
    }

    // Extract the <title> tag
    const title = $('title').text();

    // Extract the first apple-touch-icon
    const appleTouchIcon =
      $('link[rel="apple-touch-icon"]').first().attr('href') ?? '';

    return {
      meta_tags: metaTags,
      title,
      apple_touch_icon: appleTouchIcon,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getBusinessDetailsFromWebsite = async (params: {
  website: string;
}): Promise<IBusinessDetailsFromWebsite> => {
  try {
    const extractedMetaData = await getWebsiteMetaData(params);
    const prompt = getMetaDataExtractionPrompt(extractedMetaData);
    const openAiResponse = (await getResponseFromAzureOpenAi(
      prompt,
    )) as IBusinessDetailsFromWebsite;
    return openAiResponse;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

// using Ideogram
export const generateAiAdBanner = async (
  params: {
    campaign_id: string;
    banner_details: {
      creative_title: string;
      call_out: string;
      call_to_action: string;
      focused_usp: string;
      imagery: string;
    };
    size: 'square' | 'portrait';
    vendor?: 'ideogram' | 'openai' | 'gemini';
  },
  user?: IAuthUser,
): Promise<{
  hash: string;
  width: number;
  height: number;
  s3_url: string;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  const {
    campaign_id: campaignId,
    banner_details: bannerDetails,
    size,
    vendor = 'ideogram',
  } = params;

  if (!['square', 'portrait'].includes(size)) {
    throw new ValidationError(400, 'size must be one of square or portrait');
  }
  if (
    !bannerDetails.creative_title ||
    !bannerDetails.call_out ||
    !bannerDetails.call_to_action ||
    !bannerDetails.imagery
  ) {
    throw new ValidationError(400, 'Incomplete banner_details');
  }
  try {
    const campaign = await getCampaignDetails(campaignId, user);
    if (!campaign) {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
    if (!campaign.details?.targeting || !campaign.details?.business_details) {
      throw new ValidationError(400, 'This campaign has missing details');
    }
    const imageGenPromptGeneratorPrompt = getAiBannerImageGenPrompt({
      targeting: campaign.details?.targeting,
      business_details: campaign.details?.business_details,
      banner_details: bannerDetails,
      google_geo_locations: campaign.google_ads_data?.geo_locations,
      tiktok_geo_locations: campaign.tiktok_ads_data?.geo_locations,
    });
    const openAiResponse = await getResponseFromAzureOpenAi(
      imageGenPromptGeneratorPrompt,
    );
    const imageGenPrompt = openAiResponse.prompt;
    // logger.debug(imageGenPrompt);

    let aiBannerImagePath = '';

    // random number to handle rare cases of parallel requests getting processed at same time
    const fileName = `${Date.now()}-${user.uid}-${campaign.id}-${Math.random()
      .toString(36)
      .substring(2, 8)}.png`;

    if (vendor === 'openai') {
      const aiAdBannerImageData = await generateAiBannerImageFromOpenAi({
        prompt: imageGenPrompt,
        aspect_ratio: size === 'square' ? '1x1' : '9x16',
      });

      // write this image to local file
      aiBannerImagePath = await saveFileToDisk({
        fileName,
        url: aiAdBannerImageData.data?.[0]?.b64_json,
        directory: TMP_ADIMAGES_UPLOAD_DIR,
      });
    } else if (vendor === 'gemini') {
      // Convert current banner details to V3 structure for better prompt generation
      const bannerDetailsV3 = {
        headline_text: bannerDetails.creative_title,
        subheadlines_or_usp: [
          bannerDetails.call_out,
          bannerDetails.focused_usp,
        ].filter(Boolean),
        call_to_action: bannerDetails.call_to_action,
        visual_background_image_type: [bannerDetails.imagery],
        contact_details:
          campaign.details?.business_details?.mobile ||
          campaign.details?.business_details?.website ||
          '',
        design_notes: ['Clean, professional design', 'Mobile-optimized layout'],
      };

      // Use V3 prompt style for better image generation
      const imageGenPromptV3 = getAiBannerImageGenPromptV3({
        banner_details: bannerDetailsV3,
      });

      const aiAdBannerImageData = await generateImageWithImagen4({
        prompt: imageGenPromptV3,
        aspect_ratio: size === 'square' ? '1x1' : '9x16',
        quality: 'high',
        model: 'imagen-4.0-ultra-generate-001',
      });

      aiBannerImagePath = await saveFileToDisk({
        fileName,
        url: aiAdBannerImageData.data?.[0]?.b64_json,
        directory: TMP_ADIMAGES_UPLOAD_DIR,
      });
    } else {
      const aiAdBannerImageData = await generateAiBannerImageFromIdeogram({
        prompt: imageGenPrompt,
        aspect_ratio: size === 'square' ? '1x1' : '9x16',
      });

      // write this image to local file
      aiBannerImagePath = await saveFileToDisk({
        // random number to handle rare cases of parallel requests getting processed at same time
        fileName,
        url: aiAdBannerImageData.data?.[0]?.url,
        directory: TMP_ADIMAGES_UPLOAD_DIR,
      });
    }

    // Upload to Meta and S3
    const file = await createMulterFile(aiBannerImagePath);
    const response = await uploadAdImage(file, {
      ad_account_id: campaign.details?.config?.ad_account_id,
    });
    if (response?.data?.images) {
      const uploadedMetaImageData = Object.values(
        response?.data?.images,
      )?.[0] as {
        hash: string;
        width: number;
        height: number;
      };
      return {
        hash: uploadedMetaImageData.hash,
        width: uploadedMetaImageData.width,
        height: uploadedMetaImageData.height,
        s3_url: response.data.s3_url,
      };
    } else {
      throw new Error('Failed to upload image to Meta');
    }
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

// using Openai gpt-image
export const generateAiAdBannerForAdCampaign = async (
  params: {
    campaign_id: string;
  },
  user?: IAuthUser,
): Promise<{
  hash: string;
  width: number;
  height: number;
  s3_url: string;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  const { campaign_id: campaignId } = params;

  try {
    const campaign = await getCampaignDetails(campaignId, user);
    if (!campaign) {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
    if (!campaign.details?.targeting || !campaign.details?.business_details) {
      throw new ValidationError(400, 'This campaign has missing details');
    }

    const uspAndBannerElementDetailsPromptV2 =
      getUspAndBannerElementDetailsPromptV2({
        business_details: campaign.details.business_details,
        ai_assisted_product_usps: campaign.details.ai_assisted_product_usps,
      });

    const uspAndBannerElementDetailsPromptResponse =
      (await getResponseFromAzureOpenAi(
        uspAndBannerElementDetailsPromptV2,
      )) as {
        banners: Array<{
          headline_text: string;
          subheadlines_or_usp: string[];
          call_to_action: string;
          visual_background_image_type: string[];
          contact_details: string;
          design_notes: string[];
        }>;
      };

    if (uspAndBannerElementDetailsPromptResponse.banners.length === 0)
      throw new Error('missing banner details');

    const aiBannerImageGenPromptV3 = getAiBannerImageGenPromptV3({
      banner_details: uspAndBannerElementDetailsPromptResponse.banners[0],
    });
    // console.log(aiBannerImageGenPromptV3);
    // logger.debug('for testing prompt => ', aiBannerImageGenPromptV3);

    // random number to handle rare cases of parallel requests getting processed at same time
    const fileName = `${Date.now()}-${user.uid}-${campaign.id}-${Math.random()
      .toString(36)
      .substring(2, 8)}.png`;

    const aiAdBannerImageData = await generateAiBannerImageFromOpenAi({
      prompt: aiBannerImageGenPromptV3,
      aspect_ratio: '1x1',
      quality: 'high',
    });

    // write this image to local file
    const aiBannerImagePath = await saveFileToDisk({
      fileName,
      url: aiAdBannerImageData.data?.[0]?.b64_json,
      directory: TMP_ADIMAGES_UPLOAD_DIR,
    });

    // Upload to Meta and S3
    const file = await createMulterFile(aiBannerImagePath);
    const response = await uploadAdImage(file, {
      ad_account_id: campaign.details?.config?.ad_account_id,
    });
    if (response?.data?.images) {
      const uploadedMetaImageData = Object.values(
        response?.data?.images,
      )?.[0] as {
        hash: string;
        width: number;
        height: number;
      };
      return {
        hash: uploadedMetaImageData.hash,
        width: uploadedMetaImageData.width,
        height: uploadedMetaImageData.height,
        s3_url: response.data.s3_url,
      };
    } else {
      throw new Error('Failed to upload image to Meta');
    }
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const getAdLanguageSuggestions = async (
  campaignId: string,
  user?: IAuthUser,
): Promise<{
  language_suggestions: Array<{
    language: AdLanguage;
    reason: string;
  }>;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const campaign = await getCampaignDetails(campaignId, user);
    if (campaign) {
      if (campaign?.details?.targeting && campaign?.details?.business_details) {
        const prompt = getAdLanguageSuggestonsPrompt({
          business_details: campaign.details.business_details,
          targeting: campaign.details.targeting,
          google_geo_locations: campaign.google_ads_data?.geo_locations,
          tiktok_geo_locations: campaign.tiktok_ads_data?.geo_locations,
        });
        const openAiResponse = await getResponseFromOpenAi(prompt);
        return openAiResponse;
      } else {
        throw new ValidationError(400, 'This campaign has missing details');
      }
    } else {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

// flow_type: usps | banners
export const getUspAndBannerElementDetails = async (
  queryParams: Record<string, string>,
  user?: IAuthUser,
): Promise<{
  banners: Array<{
    creative_title: string;
    call_out: string;
    call_to_action: string;
    focused_usp: string;
  }>;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const { campaign_id: campaignId, flow_type: flowType } = queryParams;
    const uspsOnly = flowType === 'usps';

    const campaign = await getCampaignDetails(campaignId, user);
    if (campaign) {
      if (campaign?.details?.targeting && campaign?.details?.business_details) {
        const prompt = getUspAndBannerElementDetailsPrompt({
          business_details: campaign.details.business_details,
          targeting: campaign.details.targeting,
          ad_language: uspsOnly
            ? AdLanguage.ENGLISH
            : campaign.details?.ad_language,
          ai_assisted_product_usps: uspsOnly
            ? []
            : campaign.details?.ai_assisted_product_usps,
        });
        const openAiResponse = await getResponseFromOpenAi(prompt);
        return openAiResponse;
      } else {
        throw new ValidationError(400, 'This campaign has missing details');
      }
    } else {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getIdealCustomers = async (
  queryParams: Record<string, string>,
  user?: IAuthUser,
): Promise<{
  ideal_cusomters: string;
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }
  try {
    const { campaign_id: campaignId } = queryParams;

    const campaign = await getCampaignDetails(campaignId, user);

    if (campaign) {
      if (campaign?.details?.business_details) {
        const prompt = getIdealCustomersPrompt({
          business_details: campaign.details.business_details,
        });
        const openAiResponse = await getResponseFromOpenAi(prompt);
        return openAiResponse;
      } else {
        throw new ValidationError(400, 'This campaign has missing details');
      }
    } else {
      throw new ValidationError(
        400,
        'This campaign does not exist or you do not have permission to access it.',
      );
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getMetaAudienceV2 = async (payload: {
  targeting: ITargeting;
  businessDetails: IBusinessDetails;
}): Promise<Partial<IFlexibleSpecItem>> => {
  try {
    const { targeting, businessDetails } = payload;

    const targetingItems: IFlexibleTargetingItem[] = [];
    const flexibleSpecItem: Partial<IFlexibleSpecItem> = {
      behaviors: [],
      interests: [],
      life_events: [],
      education_statuses: [],
      industries: [],
    };

    // get audience keywords first
    const keywordsPrompt = getMetaAudienceKeywordsPrompt({
      targeting,
      business_details: businessDetails,
    });
    const keywordsResponse = await getResponseFromOpenAi(keywordsPrompt);
    const interestsKeywords = keywordsResponse?.keywords as string[];
    logger.info(interestsKeywords);

    // get audience from these keywords from Meta
    const responsePromises = interestsKeywords.map(async (keyword) => {
      return await getAudienceSearch({
        q: keyword,
        limit: '10',
        ad_account_id: GROWEASY_MAIN_AD_ACC_ID,
      });
    });
    const responses = await Promise.all(responsePromises);
    responses.forEach((response) => {
      targetingItems.push(
        ...(response.data?.data as IFlexibleTargetingItem[]).map((item) => ({
          id: item.id,
          name: item.name,
          type: item.type,
        })),
      );
    });

    // Get targeting based on selected business category
    const categoryBasedTargetingSpec:
      | Record<string, IFlexibleTargetingItem>
      | undefined = businessCategoriesWithAudience.find(
      (item) =>
        item['business-category']?.trim() ===
        businessDetails.business_category?.trim(),
    )?.audiences;

    // combine both audiences
    if (categoryBasedTargetingSpec) {
      targetingItems.push(...Object.values(categoryBasedTargetingSpec));
    }

    // prompt to label audiences
    const dedupedTargetingItems = deduplicateTargetingItems(targetingItems);
    const prompt = getFlexibleSpecTargetingPrompts(
      { targeting, business_details: businessDetails },
      JSON.stringify(dedupedTargetingItems),
    );
    const filteresAudienceList = (await getResponseFromOpenAi(prompt))
      ?.targeting as IFlexibleTargetingItem[];

    // Map targeting items to flexibleSpecItem
    filteresAudienceList.forEach((item) => {
      if (item.conversion_probability === 'high') {
        delete item.conversion_probability;
        const fullTargetingItem = dedupedTargetingItems.find(
          (i) => i.id === item.id,
        );

        if (fullTargetingItem?.type) {
          if (flexibleSpecItem[fullTargetingItem.type]) {
            flexibleSpecItem[fullTargetingItem.type]?.push(
              fullTargetingItem.type ===
                FlexibleTargetingItemType.education_statuses
                ? item.id
                : item,
            );
          } else {
            flexibleSpecItem[fullTargetingItem.type] = [
              fullTargetingItem.type ===
              FlexibleTargetingItemType.education_statuses
                ? item.id
                : item,
            ];
          }
        } else {
          logger.error(
            'Flexible spec targeting data mismatch or Missing type in Targeting data set',
          );
        }
      }
    });

    return flexibleSpecItem;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data);
    throw error;
  }
};

import { type AxiosError } from 'axios';
import bannerBasedVideoTemplates from '../constants/banner_based_video_templates';
import { generateBannerBasedVideoAd } from '../modules/ffmpeg';
import logger from '../modules/logger';
import { getResponseFromAzureOpenAi } from '../modules/openai';
import { type IBusinessDetailsFromWebsite, type IAuthUser } from '../types';
import {
  type IVideoDataV2,
  type IBannerBasedVideoTemplate,
} from '../types/video_template';
import ValidationError from '../utils/validation_error';
import { getCampaignDetails } from './db_controller';
import { getBusinessDetailsFromWebsite } from './openai_controller';
import { getVideoMakerPromptV2 } from '../prompts/groweasy_prompts';
import { createVideo } from '../modules/fc-video-service';
import { createVideo as createRemotionVideo } from '../modules/remotion';
import { FcVideoTemplates } from '../modules/fc-video-service/types';
import {
  FpVideoTemplates,
  type IFpVideoData,
} from '../modules/fp-video-service/types';
import { type IImageData, type IPexelsVideoData } from '../types/stock_image';
import { getSampleVideoData } from '../modules/fp-video-service';
import {
  getImageAndVideoKeywordsPrompt,
  getVideoDataPrompt,
} from '../prompts/bannerbot_prompts';
import { getImagesFromFreepik } from '../modules/freepik';
import { getVideosFromPexels } from '../modules/pexels';
import { AdLanguage } from '../types/campaign_details';
import { getVideoDataUsingDetailsV2 } from './bannerbot/video/bannerbot_video_v2_controller';
import { getHdVideoUrlsFromPexelsData } from '../utils';

// single frame video with bg music, no human voiceover
export const getBannerBasedVideoTemplate = async (
  data: {
    campaignId: string;
  },
  user?: IAuthUser,
): Promise<IBannerBasedVideoTemplate> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  const { campaignId } = data;

  const campaignDetails = await getCampaignDetails(campaignId, user);

  // create a copy of template so that original one stays unmodified
  const videoTemplate = JSON.parse(
    JSON.stringify(bannerBasedVideoTemplates[0]),
  ) as IBannerBasedVideoTemplate;

  // populate template with campaign banner details
  const squareBannersWithData = campaignDetails.details?.ad_banners?.filter(
    (item) => !!item.banner_data,
  );
  if (!squareBannersWithData?.length) {
    throw new ValidationError(400, 'Campaign banners have missing data');
  }

  const squareBanner = squareBannersWithData[0];
  videoTemplate.video_caption = `${squareBanner.banner_data
    ?.creative_title}-${Date.now()}`;
  videoTemplate?.frames?.forEach((frame) => {
    const textPropsArr = frame.textPropsArr;
    if (textPropsArr?.length) {
      // heading, description & CTA always in order
      if (textPropsArr[0]) {
        textPropsArr[0].value = squareBanner.banner_data?.creative_title ?? '';
      }
      if (textPropsArr[1]) {
        textPropsArr[1].value = squareBanner.banner_data?.call_out ?? '';
      }
      if (textPropsArr[2]) {
        textPropsArr[2].value = squareBanner.banner_data?.call_to_action ?? '';
      }
    }
    const mediaPropsArr = frame.mediaPropsArr;
    if (mediaPropsArr?.length) {
      mediaPropsArr[0].url = squareBanner.banner_data?.creative_image_url ?? '';
    }
  });

  return videoTemplate;
};

export const generateBannerBasedVideo = async (params: {
  template: IBannerBasedVideoTemplate;
}): Promise<{ url: string; caption: string }> => {
  const { template } = params;

  const data = await generateBannerBasedVideoAd(template);

  return data;
};

// multi frame video with bg music, no human voiceover
export const getBannerBasedVideoTemplateV2 = async (
  data: {
    campaignId: string;
  },
  user?: IAuthUser,
): Promise<IBannerBasedVideoTemplate> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  const { campaignId } = data;

  // create a copy of template so that original one stays unmodified
  const videoTemplate = JSON.parse(
    JSON.stringify(bannerBasedVideoTemplates[1]),
  ) as IBannerBasedVideoTemplate;

  try {
    const campaignDetails = await getCampaignDetails(campaignId, user);

    // populate template with campaign banner details
    const squareBannersWithData = campaignDetails.details?.ad_banners?.filter(
      (item) => !!item.banner_data,
    );
    if (!squareBannersWithData?.length) {
      throw new ValidationError(400, 'Campaign banners have missing data');
    }

    if (
      !campaignDetails?.details?.business_details ||
      !campaignDetails?.details?.targeting
    ) {
      throw new ValidationError(400, 'Campaign has missing details');
    }

    let businessDetailsFromWebsite: IBusinessDetailsFromWebsite | null = null;
    if (campaignDetails?.details?.business_details?.website) {
      try {
        businessDetailsFromWebsite = await getBusinessDetailsFromWebsite({
          website: campaignDetails.details.business_details.website,
        });
      } catch (error) {
        logger.error(
          (error as AxiosError)?.response?.data ?? (error as Error)?.message,
        );
      }
    }
    const prompt = getVideoMakerPromptV2({
      business_details: campaignDetails.details?.business_details,
      targeting: campaignDetails?.details?.targeting,
    });
    const videoData: IVideoDataV2 = await getResponseFromAzureOpenAi(prompt);
    videoTemplate.video_caption = videoData.video_caption;

    // index: 0 -> Engaging question
    // index: 1 -> Carousel 1
    // index: 2 -> Carousel 2
    // index: 3 -> Business logo, name & CTA
    videoTemplate?.frames?.forEach((frame, index) => {
      const textPropsArr = frame.textPropsArr;
      const mediaPropsArr = frame.mediaPropsArr;
      switch (index) {
        case 0: {
          // engaging_question
          if (textPropsArr?.[0] && videoData?.frames?.[0]?.highlighted_text) {
            textPropsArr[0].value = videoData.frames[0].voiceover_text;
          }
          break;
        }
        case 1: {
          // product_or_service_introduction
          if (textPropsArr?.[0] && videoData?.frames?.[1]?.highlighted_text) {
            textPropsArr[0].value = videoData.frames[1].highlighted_text;
          }
          if (textPropsArr?.[1] && videoData?.frames?.[1]?.voiceover_text) {
            textPropsArr[1].value = videoData.frames[1].voiceover_text;
          }
          if (mediaPropsArr?.[0] && squareBannersWithData?.[0]) {
            mediaPropsArr[0].url =
              squareBannersWithData[0].banner_data?.creative_image_url ?? '';
          }
          break;
        }
        case 2: {
          // content
          if (textPropsArr?.[0] && videoData?.frames?.[2]?.highlighted_text) {
            textPropsArr[0].value = videoData.frames[2].highlighted_text;
          }
          if (textPropsArr?.[1] && videoData?.frames?.[2]?.voiceover_text) {
            textPropsArr[1].value = videoData.frames[2].voiceover_text;
          }
          if (mediaPropsArr?.[0] && squareBannersWithData?.[1]) {
            mediaPropsArr[0].url =
              squareBannersWithData[1].banner_data?.creative_image_url ?? '';
          }
          break;
        }
        case 3: {
          // conclusion
          if (textPropsArr?.[0]) {
            textPropsArr[0].value =
              businessDetailsFromWebsite?.business_name ??
              campaignDetails?.details?.business_details?.mobile ??
              '';
          }
          if (textPropsArr?.[1] && videoData?.frames?.[4]?.highlighted_text) {
            textPropsArr[1].value = videoData.frames[4].highlighted_text;
          }
          /* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
          const businessLogoUrl =
            businessDetailsFromWebsite?.business_square_logo_url ||
            businessDetailsFromWebsite?.business_logo_url ||
            '';
          /* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
          if (mediaPropsArr?.[0]) {
            if (businessLogoUrl) {
              mediaPropsArr[0].url = businessLogoUrl;
            } else {
              // no logo found, remove media entry
              frame.mediaPropsArr = mediaPropsArr.filter(
                (_, index) => index !== 0,
              );
            }
          }
          break;
        }
      }
    });
  } catch (error) {
    logger.error(error);
    throw error;
  }

  return videoTemplate;
};

export const generateFcVideo = async (
  data: {
    template_id: string;
    template_data: object;
    video_caption: string;
  },
  user?: IAuthUser,
): Promise<{ s3_url: string }> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  if (!Object.keys(FcVideoTemplates).includes(data.template_id)) {
    throw new ValidationError(400, 'Bad template_id');
  }

  try {
    const response = await createVideo({
      uid: user.uid,
      videoCaption: data.video_caption,
      templateId: data.template_id as FcVideoTemplates,
      templateData: data.template_data,
    });

    return response;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

// base video background & texts overlay
export const getRemotionVideoData = async (
  data: {
    campaignId: string;
    templateId?: string;
  },
  user?: IAuthUser,
): Promise<{
  video_data: IFpVideoData;
  media_suggestions: {
    videos: IPexelsVideoData[];
    images: IImageData[];
  };
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  const { campaignId, templateId = FpVideoTemplates.p4 } = data;

  try {
    const campaignDetails = await getCampaignDetails(campaignId, user);
    const campaignBusinessDetails = campaignDetails?.details?.business_details;

    if (!campaignBusinessDetails) {
      throw new ValidationError(400, 'Campaign has missing details');
    }

    // Try to fill missing business details by scraping website
    let businessDetailsFromWebsite: IBusinessDetailsFromWebsite | null = null;
    if (campaignBusinessDetails?.website) {
      try {
        businessDetailsFromWebsite = await getBusinessDetailsFromWebsite({
          website: campaignBusinessDetails.website,
        });
        if (
          !campaignBusinessDetails.business_name &&
          businessDetailsFromWebsite?.business_name
        ) {
          campaignBusinessDetails.business_name =
            businessDetailsFromWebsite.business_name;
        }
        if (
          !campaignBusinessDetails?.business_logo?.square?.url &&
          businessDetailsFromWebsite?.business_square_logo_url
        ) {
          campaignBusinessDetails.business_logo = {
            square: {
              url: businessDetailsFromWebsite?.business_square_logo_url,
              width: 200,
              height: 200,
            },
          };
        }
      } catch (error) {
        logger.error(
          (error as AxiosError)?.response?.data ?? (error as Error)?.message,
        );
      }
    }
    const sampleVideoData = getSampleVideoData(templateId as FpVideoTemplates);
    if (!sampleVideoData) {
      throw new Error('sampleVideoData not found for template');
    }
    const videoDataPrompt = getVideoDataPrompt({
      businessDetails: {
        ...campaignBusinessDetails,
        key_benefits: campaignDetails?.details?.ai_assisted_product_usps,
      },
      sampleVideoData,
      language: campaignDetails?.details?.ad_language ?? AdLanguage.ENGLISH,
    });
    const videoData = (await getResponseFromAzureOpenAi(
      videoDataPrompt,
    )) as IFpVideoData;

    // remove the logo node if the url is not present
    if (videoData?.branding?.logo?.url === '') {
      videoData.branding.logo = undefined;
    }

    // special handling for p4, overriding scene 5 texts with brand contact details
    // going forward, FE templates should consume contact details from brand info. There shouldn't be two sources of same info
    if (templateId === FpVideoTemplates.p4 && videoData.scenes?.[4]?.texts) {
      videoData.scenes[4].texts = [
        {
          value: `Contact: ${campaignBusinessDetails?.mobile} ${
            campaignBusinessDetails.website
              ? 'Or visit: ' + campaignBusinessDetails.website
              : ''
          }`,
        },
      ];
    }

    // populate images & videos
    const mediaKeywordsPrompt = getImageAndVideoKeywordsPrompt({
      businessDetails: campaignBusinessDetails,
    });
    const openAiResponseForMediaKeywords = (await getResponseFromAzureOpenAi(
      mediaKeywordsPrompt,
    )) as {
      image_keywords: string[];
      video_content_description_involving_people: string;
    };
    logger.info(openAiResponseForMediaKeywords);
    const relevantStockImages = await getImagesFromFreepik(
      openAiResponseForMediaKeywords.image_keywords?.join(', '),
      '25',
    );
    const bannerImages = campaignDetails?.details?.ad_banners
      ?.filter((item) => !item.hidden && !!item.banner_data?.creative_image_url)
      ?.map((item) => item.banner_data?.creative_image_url);
    const relevantVideos = await getVideosFromPexels({
      queries: [
        openAiResponseForMediaKeywords.video_content_description_involving_people,
      ],
      orientation: 'portrait',
    });
    // only HD videos
    const relevantHdVideosLinks: string[] = getHdVideoUrlsFromPexelsData(
      relevantVideos,
    ).map((item) => item.url);
    // for (const relevantVideo of relevantVideos) {
    //   const hdVideoUrl = relevantVideo.video_files?.find(
    //     (item) => item.quality === 'hd',
    //   )?.link;
    //   if (hdVideoUrl) {
    //     relevantHdVideosLinks.push(hdVideoUrl);
    //   }
    // }

    let stockImageIndex = 0;
    let bannerImageIndex = 0;
    let videoIndex = 0;

    // populate images & videos
    videoData.scenes?.forEach((scene) => {
      scene.assets?.forEach((asset) => {
        if (asset.type === 'image') {
          if (bannerImages?.[bannerImageIndex]) {
            asset.url = bannerImages[bannerImageIndex++] ?? '';
          } else {
            asset.url =
              relevantStockImages[stockImageIndex % relevantStockImages.length]
                ?.url;
            stockImageIndex++;
          }
        } else if (asset.type === 'video' && relevantHdVideosLinks.length) {
          asset.url =
            relevantHdVideosLinks[videoIndex % relevantHdVideosLinks.length];
          videoIndex++;
        }
      });
    });

    // special handling for base video
    if (
      videoData?.base_assets?.[0]?.type === 'video' &&
      relevantHdVideosLinks[0]
    ) {
      videoData.base_assets[0].url = relevantHdVideosLinks[0];
    }
    return {
      video_data: videoData,
      media_suggestions: {
        videos: relevantVideos,
        images: relevantStockImages,
      },
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getRemotionVideoDataV2 = async (
  data: {
    campaignId: string;
    templateId?: string;
    useStoryblocks?: boolean;
  },
  user?: IAuthUser,
  retryCount: number = 0,
): Promise<{
  video_data: IFpVideoData;
  media_suggestions: {
    videos: IPexelsVideoData[];
    images: IImageData[];
  };
}> => {
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  const { campaignId, templateId = FpVideoTemplates.p4 } = data;

  try {
    const campaignDetails = await getCampaignDetails(campaignId, user);
    const campaignBusinessDetails = campaignDetails?.details?.business_details;

    if (!campaignBusinessDetails) {
      throw new ValidationError(400, 'Campaign has missing details');
    }

    // Try to fill missing business details by scraping website
    let businessDetailsFromWebsite: IBusinessDetailsFromWebsite | null = null;
    if (campaignBusinessDetails?.website) {
      try {
        businessDetailsFromWebsite = await getBusinessDetailsFromWebsite({
          website: campaignBusinessDetails.website,
        });
        if (
          !campaignBusinessDetails.business_name &&
          businessDetailsFromWebsite?.business_name
        ) {
          campaignBusinessDetails.business_name =
            businessDetailsFromWebsite.business_name;
        }
        if (
          !campaignBusinessDetails?.business_logo?.square?.url &&
          businessDetailsFromWebsite?.business_square_logo_url
        ) {
          campaignBusinessDetails.business_logo = {
            square: {
              url: businessDetailsFromWebsite?.business_square_logo_url,
              width: 200,
              height: 200,
            },
          };
        }
      } catch (error) {
        logger.error(
          (error as AxiosError)?.response?.data ?? (error as Error)?.message,
        );
      }
    }

    const videoDataResponse = await getVideoDataUsingDetailsV2({
      businessDetails: {
        ...campaignBusinessDetails,
        key_benefits: campaignDetails?.details?.ai_assisted_product_usps,
      },
      templateId: templateId as FpVideoTemplates,
      language: campaignDetails?.details?.ad_language ?? AdLanguage.ENGLISH,
      generateAiBaseVideo: false,
      getMediaSuggestions: true,
      useStoryblocks: data.useStoryblocks,
    });

    if (!videoDataResponse.videoData || !videoDataResponse.mediaSuggestion)
      throw new Error(
        'missing videoData or mediaSuggestions in videoDataResponse',
      );

    return {
      video_data: videoDataResponse.videoData,
      media_suggestions: {
        videos: videoDataResponse.mediaSuggestion ?? [],
        images: [],
      },
    };
  } catch (error) {
    if (retryCount > 0) {
      return await getRemotionVideoDataV2(data, user, retryCount - 1);
    }
    logger.error(error);
    throw error;
  }
};

export const generateRemotionVideo = async (
  params: {
    campaignId: string;
    templateData: IFpVideoData;
  },
  user?: IAuthUser,
  retryCount: number = 0,
): Promise<{ s3_url: string }> => {
  const { campaignId, templateData } = params;
  if (!user?.uid) {
    throw new ValidationError(400, 'Missing uid');
  }

  if (!Object.keys(FpVideoTemplates).includes(templateData.template_id)) {
    throw new ValidationError(400, 'Bad template_id');
  }

  try {
    const timestamp = Date.now();
    const videoId = `${templateData.template_id}_${timestamp}`;

    const createVideoResponse = await createRemotionVideo({
      uid: user.uid,
      templateData,
      campaignId,
      videoId,
    });
    // save video link in project details
    logger.info(
      `createVideo response ${createVideoResponse.s3_url} for video id: ${videoId}`,
    );
    return {
      s3_url: createVideoResponse.s3_url,
    };
  } catch (error) {
    if (retryCount > 0) {
      return await generateRemotionVideo(params, user, retryCount - 1);
    }
    logger.error(error);
    throw error;
  }
};

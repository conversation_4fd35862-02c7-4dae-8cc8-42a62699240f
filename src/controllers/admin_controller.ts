import {
  ADGLOBAL_AI_BE_MASTER_GOOGLE_SHEET_ID,
  FIRESTORE_COLLECTIONS,
  META_BASE_URL,
} from '../constants';
import logger from '../modules/logger';
import {
  type IUserProfile,
  type IAuthUser,
  GrowEasyPartners,
  type ISelfAdAccountConfigs,
  AdPlatforms,
} from '../types';
import {
  Currency,
  GROWEASY_CAMPAIGN_TYPE,
  GrowEasyCampaignStatus,
  type ICampaign,
} from '../types/campaign_details';
import { auth, db } from '../modules/firebase_admin_sdk';
import { Timestamp, type DocumentData } from 'firebase-admin/firestore';
import {
  type IBillingDetails,
  type IOrderDetails,
  type IInvoiceDetails,
} from '../types/payments_invoices';
import { appendDataToSheet } from '../modules/google_sheet';
import ValidationError from '../utils/validation_error';
import {
  getCampaignCurrencyBudgetNode,
  getMetaUrl,
  isAdGlobalAiAdmin,
  updateFirestoreCampaign,
} from './util';
import {
  getTargetingFromCampaign,
  updateAdImage,
  updateMetaCampaign,
} from './meta_controller';
import { updateGoogleCampaign } from './google/google_controller';
import {
  getGoogleAdsDataFromSubCollection,
  getTiktokAdsDataFromSubCollection,
} from './db_controller';
import { updateTiktokCampaign } from './tiktok/tiktok_controller';
import axios from 'axios';

// ?status=ACTIVE,DRAFT (comma separated)
export const getAdminCampaigns = async (
  queryParams: Record<string, any>,
  user?: IAuthUser,
): Promise<{
  campaigns: ICampaign[];
  last_cursor_id: string;
}> => {
  try {
    const {
      last_cursor_id: lastCursorId,
      limit = `20`,
      email = '',
    } = queryParams;

    let filterUid: string | null = null;

    if (email) {
      try {
        const firebaseUser = await auth.getUserByEmail(email);
        filterUid = firebaseUser.uid;
      } catch (err) {
        logger.error(`No user found with email ${email}`);
        return {
          campaigns: [],
          last_cursor_id: '',
        };
      }
    }

    const lastDocRef = lastCursorId
      ? await db
          .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
          .doc(lastCursorId)
          .get()
      : null;

    const campaigns: ICampaign[] = [];
    const status = queryParams.status;
    let statusArr = [
      GrowEasyCampaignStatus.ACTIVE,
      GrowEasyCampaignStatus.PAUSED,
      GrowEasyCampaignStatus.DRAFT,
    ];
    if (status) {
      statusArr = status.split(',');
    }

    let query = db
      .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
      .where('status', 'in', statusArr)
      .orderBy('updated_at', 'desc');
    // .orderBy('created_at', 'desc')

    if (isAdGlobalAiAdmin(user)) {
      query = query.where('details.config.partner', '==', 'AD_GLOBAL_AI');
    }

    if (filterUid) {
      query = query.where('uid', '==', filterUid);
    }

    if (lastDocRef) {
      query = query.startAfter(lastDocRef);
    }

    const querySnapshot = await query.limit(parseInt(limit)).get();

    querySnapshot.forEach((doc) => {
      const data = doc.data() as ICampaign;
      campaigns.push(data);
    });

    const newLastDocRef = querySnapshot.docs[querySnapshot.docs.length - 1];

    return {
      campaigns,
      last_cursor_id: newLastDocRef?.id ?? '',
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getCampaignDetailsForGrowEasyAdmin = async (
  id: string,
): Promise<DocumentData | null> => {
  const snapshot = await db
    .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
    .where('id', '==', id)
    .get();
  return snapshot.docs[0] ?? null;
};

export const getCampaignDetailsForAdGlobalAiAdmin = async (
  id: string,
): Promise<DocumentData | null> => {
  const snapshot = await db
    .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
    .where('id', '==', id)
    .where('details.config.partner', '==', 'AD_GLOBAL_AI')
    .get();
  return snapshot.docs[0] ?? null;
};

export const getAdminInvoices = async (
  user?: IAuthUser,
): Promise<IInvoiceDetails[] | null> => {
  try {
    const snapshot = await db
      .collection(FIRESTORE_COLLECTIONS.INVOICES)
      .orderBy('created_at', 'desc')
      .limit(100)
      .get();
    const invoices: IInvoiceDetails[] = [];
    snapshot.forEach((doc) => {
      invoices.push(doc.data() as IInvoiceDetails);
    });
    return invoices;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const updateUserCustomClaims = async (
  uid: string,
  data: object,
): Promise<object> => {
  const userDetails = await auth.getUser(uid);
  const updatedCustomClaims = {
    ...userDetails.customClaims,
    ...data,
  };
  await auth.setCustomUserClaims(uid, updatedCustomClaims);
  return updatedCustomClaims;
};

// https://docs.google.com/spreadsheets/d/10g3pHtERNHFChg1n-2g8DphbA9mKG9lU/edit
// https://docs.google.com/spreadsheets/d/1SQ7m_b-kwPEZVGr1lObNpRqCFIaJdxaJsem7tZUoFrE/edit?gid=595669894#gid=595669894
export const writeOrderToBusinessTransactionsSheet = async (params: {
  order: IOrderDetails;
}): Promise<void> => {
  const { order } = params;

  logger.info(
    `Processing order: ${order.razorpay_order_id} created at ${order.updated_at
      .toDate()
      .toISOString()}`,
  );

  // get campaign details
  const campaignDetails = (
    await getCampaignDetailsForGrowEasyAdmin(order.campaign_id)
  )?.data() as ICampaign;

  // get leads count
  let leadsCount = 0;
  if (campaignDetails) {
    const campaignType =
      campaignDetails.type ?? GROWEASY_CAMPAIGN_TYPE.LEAD_FORM;
    if (campaignType === GROWEASY_CAMPAIGN_TYPE.LEAD_FORM) {
      if (campaignDetails.meta_leadgen_form_id) {
        leadsCount = (
          await db
            .collection(FIRESTORE_COLLECTIONS.META_WEBHOOK_LEADS)
            .where('form_id', '==', campaignDetails.meta_leadgen_form_id)
            .count()
            .get()
        )?.data().count;
      }
    } else if (campaignType === GROWEASY_CAMPAIGN_TYPE.CTWA) {
      leadsCount = (
        await db
          .collection(FIRESTORE_COLLECTIONS.CTWA_LEADS)
          .where('campaign_id', '==', campaignDetails.id)
          .count()
          .get()
      )?.data().count;
    }
  }

  // get billing details
  const billingDetails = (
    await db
      .collection(FIRESTORE_COLLECTIONS.BILLING_DETAILS)
      .doc(order.uid)
      .get()
  ).data() as IBillingDetails;

  // get user details
  const userDetails = (
    await db.collection(FIRESTORE_COLLECTIONS.USER_PROFILE).doc(order.uid).get()
  ).data() as IUserProfile;

  // payment from these emails are test payments, not counting
  const testUserEmails = ['<EMAIL>', '<EMAIL>'];

  if (testUserEmails.includes(userDetails?.email)) {
    // ignore
  } else {
    const orderDate = order.updated_at.toDate();

    const orderDay = String(orderDate.getDate()).padStart(2, '0'); // Ensure 2-digit day
    const orderMonth = String(orderDate.getMonth() + 1).padStart(2, '0'); // Ensure 2-digit month
    const orderYear = orderDate.getFullYear();
    // paise to INR, cents to USD etc - for 2 decimal currencies
    const orderAmountInGivenCurrency =
      order.amount /
      ([Currency.IDR, Currency.VND].includes(order.currency) ? 1 : 100);
    const campaignCurrencyBudgetNode = getCampaignCurrencyBudgetNode(
      campaignDetails?.details?.budget_and_scheduling,
    );
    const orderAmountInInr =
      (campaignCurrencyBudgetNode?.exchange_rate ?? 1) *
      orderAmountInGivenCurrency;
    const orderType = order.type ?? 'launch';

    // write data to sheet
    await appendDataToSheet({
      sheetName: 'BusinessTransactions',
      rowData: [
        [
          '', // leave empty for S.No
          `${orderMonth}-${orderYear}`, // Month-Year
          `${orderDay}/${orderMonth}/${orderYear}`, // Date
          userDetails?.email ?? 'NA', // Customer Email
          campaignDetails?.name, // Campaign Name
          `${order.razorpay_order_id ?? ''}`, // Txn Id of Payment Gateway
          orderAmountInGivenCurrency, // Amount Received - Actual Currency
          orderAmountInInr, // Amount Received - Currency Adjusted (87 INR to USD )
          orderAmountInInr * 0.2, // Groweasy Take
          orderType === 'launch' ? leadsCount : 'Campaign Extension Payment', // Roi = Number of leads generated
          `${campaignDetails?.id ?? ''}`, // Campaign Id
          order.currency, // Currency
          billingDetails?.business_tax_id ?? 'NA', // GST Number
          billingDetails?.business_name ?? 'NA', // Business Details
          billingDetails?.business_address ?? 'NA', // Address

          /* `${order.amount / 100}`, // Amount
          campaignDetails?.name, // campaign name
          campaignDetails?.type ?? GROWEASY_CAMPAIGN_TYPE.LEAD_FORM, // campaign platform
          userDetails?.email ?? 'NA', // user email
          userDetails?.mobile ?? 'NA', // user mobile
          userDetails?.name ?? 'NA', // user name
          `${order.razorpay_order_id ?? ''}`, // Order Id
          `${
            order.razorpay_payment_id ??
            order.stripe_payment_intent_id ??
            'NA'
          }`, // Payment Id
          order.type ?? 'launch', // Order type: launch/extend
          leadsCount, // Leads count
          billingDetails?.business_tax_id ?? 'NA', // GST No
          `${billingDetails?.business_name ?? ''}, ${
            billingDetails?.billing_email ?? ''
          }, ${billingDetails?.billing_mobile ?? ''}`, // billing details
          billingDetails?.business_address, // billing address */
        ],
      ],
    });
  }
};

export const syncPaidOrdersToSheet = async (): Promise<void> => {
  // upto which date paid order data has to be synced
  // refer sheet to get last date value and update here
  const tillDate = new Date('2025-03-04');
  try {
    const ordersSnapshot = await db
      .collection(FIRESTORE_COLLECTIONS.ORDERS)
      .where('status', '==', 'paid')
      .where('updated_at', '>', tillDate)
      .orderBy('updated_at', 'asc')
      // .limit(5)
      .get();
    const orders: IOrderDetails[] = [];
    ordersSnapshot.forEach((doc) => {
      orders.push(doc.data() as IOrderDetails);
    });

    logger.info(`Total orders count: ${orders.length}`);

    // for each order, get campaign details & billing details
    for (const order of orders) {
      await writeOrderToBusinessTransactionsSheet({
        order,
      });
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getUserProfileDetails = async (queryParams: {
  email?: string;
  uid?: string;
}): Promise<IUserProfile | null> => {
  try {
    const { email, uid } = queryParams;

    if (!email && !uid) {
      throw new Error('Either email or uid is required');
    }

    let query = db
      .collection(FIRESTORE_COLLECTIONS.USER_PROFILE)
      // Sort by most recent since same email user might have deleted his old account and created new
      .orderBy('created_at', 'desc')
      .limit(1);

    if (email) {
      query = query.where('email', '==', email);
    } else if (uid) {
      query = query.where('uid', '==', uid);
    }

    const snapshot = await query.get();

    if (snapshot.empty) {
      return null; // No matching user
    }

    const userProfile = snapshot.docs[0].data() as IUserProfile;

    return userProfile;
  } catch (error) {
    logger.error('Error searching user by email/uid:', error);
    throw error;
  }
};

// supported campaign fields: name, uid, details.ad_banners
export const updateUserCampaign = async (
  campaignId: string,
  body: Partial<ICampaign>,
): Promise<DocumentData | null> => {
  if (!body) {
    throw new ValidationError(400, 'Missing details to be updated');
  }
  try {
    const doc = await getCampaignDetailsForGrowEasyAdmin(campaignId);
    const campaign = doc?.data() as ICampaign;
    if (doc?.id) {
      // include platform data too
      if (campaign.platform === AdPlatforms.GOOGLE) {
        campaign.google_ads_data = await getGoogleAdsDataFromSubCollection(
          doc.id,
        );
      } else if (campaign.platform === AdPlatforms.TIKTOK) {
        campaign.tiktok_ads_data = await getTiktokAdsDataFromSubCollection(
          doc.id,
        );
      }

      // only selected fields can be updated by admin
      const partialCampaign: Partial<ICampaign> = {
        uid: body.uid ?? campaign.uid,
        updated_at: Timestamp.now(),
        name: body.name ?? campaign.name,
        details: {
          ...campaign.details,
          ad_banners: [
            ...(campaign.details?.ad_banners ?? []),
            ...(body.details?.ad_banners ?? []),
          ],
        },
      };
      // merge
      await updateFirestoreCampaign(doc.id, partialCampaign);
      const updatedDoc = await db
        .collection(FIRESTORE_COLLECTIONS.CAMPAIGNS)
        .doc(doc.id)
        .get();
      if (campaign?.meta_id) {
        if (body.name) {
          // update name in ads manager
          await updateMetaCampaign(campaign.meta_id, {
            name: partialCampaign.name,
          });
        }
        if (body.details?.ad_banners?.[0]?.image) {
          // update ad0 creative
          await updateAdImage(campaign, {
            image: body.details.ad_banners[0].image,
          });
        }
      } else if (campaign?.google_ads_data?.campaign_resource) {
        await updateGoogleCampaign(
          campaign.google_ads_data.campaign_resource,
          {
            name: partialCampaign.name,
          },
          campaign.details?.config?.google_ad_account_id,
        );
      } else if (campaign?.tiktok_ads_data?.campaign_id) {
        await updateTiktokCampaign({
          campaign_id: campaign?.tiktok_ads_data?.campaign_id,
          campaign_name: partialCampaign.name,
        });
      }
      return updatedDoc.data() ?? null;
    } else {
      throw new ValidationError(400, 'Campaign Not found');
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const writeCampaignLaunchedToSheet = async (
  campaign: ICampaign,
  authUser: IAuthUser,
): Promise<void> => {
  try {
    const profileDoc = await db
      .collection(FIRESTORE_COLLECTIONS.USER_PROFILE)
      .doc(authUser.uid)
      .get();
    const userProfile = profileDoc.data() as IUserProfile;

    const sheetData = [
      [
        new Date().toDateString(), // date
        authUser.email ?? '', // email
        userProfile?.business_name ?? '', // business name
        campaign?.details?.business_details?.business_category ?? '', // business category
        campaign?.details?.business_details?.product_or_service_description ??
          '', // product details
      ],
    ];

    // add to BE master sheet
    await appendDataToSheet({
      sheetName: 'CampaignLaunch',
      rowData: sheetData,
    });

    // also to AdGlobalAI BE master sheet
    if (campaign?.details?.config?.partner === GrowEasyPartners.AD_GLOBAL_AI) {
      await appendDataToSheet({
        spreadsheetId: ADGLOBAL_AI_BE_MASTER_GOOGLE_SHEET_ID,
        sheetName: 'CampaignLaunch',
        rowData: sheetData,
      });
    }
  } catch (error) {
    logger.error(error);
  }
};

export const createOrUpdateSelfAdAccountConfigs = async (payload: {
  uid: string;
  configs: ISelfAdAccountConfigs;
}): Promise<ISelfAdAccountConfigs> => {
  const { uid, configs } = payload;

  const docRef = db
    .collection(FIRESTORE_COLLECTIONS.SELF_AD_ACCOUNT_CONFIGS)
    .doc(uid);

  const existingSnap = await docRef.get();
  const isNew = !existingSnap.exists;

  const timestampFields = {
    updated_at: Timestamp.now(),
    ...(isNew && { created_at: Timestamp.now() }),
  };

  await docRef.set(
    {
      ...configs,
      ...timestampFields,
    },
    { merge: true },
  );

  const updatedSnap = await docRef.get();
  return updatedSnap.data() as ISelfAdAccountConfigs;
};

// only for Meta
export const syncMetaAdsetFromCampaign = async (
  campaignId: string,
): Promise<object> => {
  if (!campaignId) {
    throw new ValidationError(400, 'Missing campaign id');
  }
  try {
    const doc = await getCampaignDetailsForGrowEasyAdmin(campaignId);
    const campaign = doc?.data() as ICampaign;
    const adsetId = campaign?.meta_adset_id;
    if (adsetId) {
      const url = getMetaUrl(`${META_BASE_URL}/${adsetId}`);
      const body = {
        targeting: getTargetingFromCampaign(campaign, true),
      };
      const response = await axios.post(url.href, body);
      return response.data;
    } else {
      throw new ValidationError(400, 'Meta Adset id not found in Campaign');
    }
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

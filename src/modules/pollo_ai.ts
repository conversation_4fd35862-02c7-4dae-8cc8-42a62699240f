import axios, { type AxiosError } from 'axios';
import logger from './logger';

const POLLO_AI_API_KEY = process.env.POLLO_AI_API_KEY ?? '';

export enum POLLO_AI_VIDEO_ROUTES {
  'SEEDANCE_1.0_LITE' = '/bytedance/seedance',
}

export enum POLLO_AI_VIDEO_RESOLUTION {
  '480p' = '480p',
  '720p' = '720p',
  '1080p' = '1080p',
}

export enum POLLO_AI_VIDEO_ASPECT_RATIO {
  RATIO_16_9 = '16:9',
  RATIO_9_16 = '9:16',
  RATIO_4_3 = '4:3',
  RATIO_3_4 = '3:4',
  RATIO_1_1 = '1:1',
}

export enum POLLO_AI_VIDEO_LENGTH {
  FIVE_SECONDS = 5,
  TEN_SECONDS = 10,
}

export const generateAiVideo = async (
  route: POLLO_AI_VIDEO_ROUTES,
  input: {
    prompt: string;
    resolution: POLLO_AI_VIDEO_RESOLUTION;
    length: POLLO_AI_VIDEO_LENGTH;
    aspectRatio: POLLO_AI_VIDEO_ASPECT_RATIO;
  },
): Promise<{ taskId: string; status: string }> => {
  const BASE_URL = 'https://pollo.ai/api/platform/generation';
  try {
    const data = {
      input,
    };

    logger.info('pollo request body -> ', data);

    const response = await axios.post(BASE_URL + route, data, {
      headers: {
        'x-api-key': POLLO_AI_API_KEY,
      },
    });

    return response.data;
  } catch (error) {
    logger.error((error as AxiosError).response?.data);
    throw error;
  }
};

interface ITaskStatusResponse {
  taskId: string;
  generations: [
    {
      id: string;
      status: 'waiting' | 'succeed' | 'failed' | 'processing';
      failMsg?: string;
      url?: string;
      mediaType: 'image' | 'video' | 'audio' | 'text';
      createdDate: string;
      updatedDate: string;
    },
  ];
}

export const getTaskStatus = async (
  taskId: string,
): Promise<ITaskStatusResponse> => {
  try {
    const url = `https://pollo.ai/api/platform/generation/${taskId}/status`;

    const response = await axios.get(url, {
      headers: { 'x-api-key': POLLO_AI_API_KEY },
    });

    return response.data;
  } catch (error) {
    logger.error((error as AxiosError).response?.data);
    throw error;
  }
};

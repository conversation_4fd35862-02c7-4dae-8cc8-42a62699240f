import axios, { AxiosError } from 'axios';
import logger from './logger';
import { type IOpenAiImageGenResponse } from './openai';

const GEMINI_API_KEY = process.env.GEMINI_API_KEY ?? '';

// Interface for Gemini Imagen response
export interface IGeminiImageGenResponse {
  predictions: Array<{
    bytesBase64Encoded: string;
    mimeType: string;
  }>;
}

// Interface for advanced Imagen 4 parameters
export interface IGeminiImageGenParams {
  prompt: string;
  aspect_ratio?: '1:1' | '9:16' | '16:9' | '3:4' | '4:3';
  numberOfImages?: number;
  imageSize?: '1K' | '2K';
  personGeneration?: 'dont_allow' | 'allow_adults' | 'allow_all';
  outputMimeType?: 'image/jpeg' | 'image/png';
  model?:
    | 'imagen-4.0-generate-001'
    | 'imagen-4.0-ultra-generate-001'
    | 'imagen-4.0-fast-generate-001';
}

/**
 * Generate images using Google Gemini Imagen 4 API
 * @param params - Configuration for image generation
 * @returns Promise<IOpenAiImageGenResponse> - Image response in OpenAI compatible format
 */
export const generateImageWithImagen4 = async (params: {
  prompt: string;
  aspect_ratio?: '1x1' | '9x16' | '16x9' | '3x4' | '4x3';
  quality?: 'high' | 'medium' | 'low';
  numberOfImages?: number;
  model?:
    | 'imagen-4.0-generate-001'
    | 'imagen-4.0-ultra-generate-001'
    | 'imagen-4.0-fast-generate-001';
}): Promise<IOpenAiImageGenResponse> => {
  try {
    // Map aspect ratios to Gemini format
    const aspectRatioMap: Record<string, string> = {
      '1x1': '1:1',
      '16x9': '16:9',
      '9x16': '9:16',
      '3x4': '3:4',
      '4x3': '4:3',
    };

    // Map quality to sample image size for Ultra and Standard models
    const sampleImageSize = params.quality === 'high' ? '2K' : '1K';

    const model = params.model ?? 'imagen-4.0-ultra-generate-001';
    const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:predict`;

    const requestBody = {
      instances: [
        {
          prompt: params.prompt,
        },
      ],
      parameters: {
        outputMimeType: 'image/jpeg',
        sampleCount: 1,
        aspectRatio: aspectRatioMap[params.aspect_ratio ?? '1:1'],
        imageSize: sampleImageSize,
        personGeneration: 'allow_all',
      },
    };

    logger.debug('Generating image with Gemini Imagen 4', {
      model,
      prompt: params.prompt,
      aspectRatio: requestBody.parameters.aspectRatio,
      sampleCount: requestBody.parameters.sampleCount,
    });

    const response = await axios.post<IGeminiImageGenResponse>(
      url,
      requestBody,
      {
        headers: {
          'x-goog-api-key': GEMINI_API_KEY,
          'Content-Type': 'application/json',
        },
        timeout: 60000, // 60 seconds timeout for image generation
      },
    );

    // Transform Gemini response to OpenAI compatible format
    const transformedResponse: IOpenAiImageGenResponse = {
      created: new Date().toISOString(),
      data: response.data.predictions.map((prediction) => ({
        b64_json: prediction.bytesBase64Encoded,
      })),
    };

    return transformedResponse;
  } catch (error) {
    logger.error('Error generating image with Gemini Imagen 4', {
      error:
        error instanceof AxiosError
          ? {
              message: error.message,
              status: error.response?.status,
              data: error.response?.data,
            }
          : error,
      prompt: params.prompt,
    });

    throw new Error(
      `Failed to generate image with Gemini Imagen 4: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
};

/**
 * Generate images using Google Gemini Imagen 4 API with advanced parameters
 * @param params - Advanced configuration for image generation
 * @returns Promise<IOpenAiImageGenResponse> - Image response in OpenAI compatible format
 */
export const generateImageWithGeminiImagen4 = async (
  params: IGeminiImageGenParams,
): Promise<IOpenAiImageGenResponse> => {
  try {
    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }

    const model = params.model ?? 'imagen-4.0-generate-001';
    const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:predict`;

    const requestBody = {
      instances: [
        {
          prompt: params.prompt,
        },
      ],
      parameters: {
        outputMimeType: params.outputMimeType ?? 'image/jpeg',
        sampleCount: Math.min(params.numberOfImages ?? 1, 4),
        aspectRatio: params.aspect_ratio ?? '1:1',
        imageSize: params.imageSize ?? '1K',
        personGeneration: params.personGeneration ?? 'allow_all',
      },
    };

    logger.debug('Generating image with Gemini Imagen 4 (Advanced)', {
      model,
      prompt:
        params.prompt.substring(0, 100) +
        (params.prompt.length > 100 ? '...' : ''),
      aspectRatio: requestBody.parameters.aspectRatio,
      sampleCount: requestBody.parameters.sampleCount,
      imageSize: requestBody.parameters.imageSize,
      personGeneration: requestBody.parameters.personGeneration,
      outputMimeType: requestBody.parameters.outputMimeType,
    });

    const response = await axios.post<IGeminiImageGenResponse>(
      url,
      requestBody,
      {
        headers: {
          'x-goog-api-key': GEMINI_API_KEY,
          'Content-Type': 'application/json',
        },
        timeout: 60000, // 60 seconds timeout for image generation
      },
    );

    if (!response.data.predictions || response.data.predictions.length === 0) {
      throw new Error('No images generated in response');
    }

    // Transform Gemini response to OpenAI compatible format
    const transformedResponse: IOpenAiImageGenResponse = {
      created: new Date().toISOString(),
      data: response.data.predictions.map((prediction) => ({
        b64_json: prediction.bytesBase64Encoded,
      })),
    };

    logger.debug(
      'Successfully generated image with Gemini Imagen 4 (Advanced)',
      {
        imagesGenerated: transformedResponse.data.length,
        model,
      },
    );

    return transformedResponse;
  } catch (error) {
    logger.error('Error generating image with Gemini Imagen 4 (Advanced)', {
      error:
        error instanceof AxiosError
          ? {
              message: error.message,
              status: error.response?.status,
              statusText: error.response?.statusText,
              data: error.response?.data,
              url: error.config?.url,
            }
          : error,
      prompt:
        params.prompt.substring(0, 100) +
        (params.prompt.length > 100 ? '...' : ''),
      model: params.model,
    });

    if (error instanceof AxiosError) {
      if (error.response?.status === 401) {
        throw new Error(
          'Invalid or missing GEMINI_API_KEY. Please check your API key.',
        );
      }
      if (error.response?.status === 403) {
        throw new Error(
          'Access denied. Please check your API key permissions and billing status.',
        );
      }
      if (error.response?.status === 429) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }
      if (error.response?.status === 400) {
        throw new Error(
          `Invalid request: ${error.response.data?.error?.message || 'Bad request'}`,
        );
      }
    }

    throw new Error(
      `Failed to generate image with Gemini Imagen 4: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
};
